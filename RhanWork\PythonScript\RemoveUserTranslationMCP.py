# mstrio-api		
from mstrio.connection import Connection
import traceback
from getpass import getpass
		
from mstrio.object_management import (		
    Translation,		
    list_translations	
)
	
mstr_base_url = "https://env-345320.customer.cloud.microstrategy.com//MicroStrategyLibrary/api"
mstr_username = "mstr"		
### mstr_password = getpass("p4j9g3FPGEKs")	
mstr_password = "p4j9g3FPGEKs"	
mstr_project_name = "MicroStrategy Tutorial"
mstr_project_id = "B7CA92F04B9FAE8D941C3E9B7E0CD754"
bVerify = False		
		
def main():	
    print("--->Start")	
    try:
        print("-- Before connection")
        CONN = Connection( base_url = mstr_base_url, 
                                username = mstr_username, 
                                password = mstr_password, 
                                project_name = mstr_project_name,
                                ### project_id = mstr_project_id
                                ssl_verify = bVerify 
                                )
        ### CONN.connect()
        print("-- After connection")
        sUserId = "A7C2E46745E03F443F03AFBB5CCA7ED1"
            
        userTran = list_translations( connection = CONN, id = sUserId, object_type = 34, project_id=CONN.project_id )[0]
        sTargetId = userTran.translation_target_id
        ### ltran = [Translation.OperationData( target_language = 1033,  target_id = sTargetId )]
        ltran = [Translation.OperationData( target_language = 1041,  target_id = sTargetId )]

        # Show project id
        print("Project id--->" + CONN.project_id)

        Translation.remove_translation(
            connection = CONN, id = sUserId, object_type = 34, translations = ltran,  project_id=CONN.project_id )
        CONN.close()
    except Exception as e:
        t = traceback.format_exception(type(e), e, e.__traceback__)
        print(t)
    
    print("End<---")

# Call the main function
if __name__ == "__main__":
    main()