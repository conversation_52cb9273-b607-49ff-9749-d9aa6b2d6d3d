-- Test Queries to Verify Table Structure and Data
-- Database: poc_metadata (PostgreSQL)
-- Purpose: Verify the MicroStrategy metadata tables before running main queries

-- =====================================================================================
-- STEP 1: EXAMINE TABLE STRUCTURES
-- =====================================================================================

-- Check the structure of dssmdobjinfo table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'dssmdobjinfo'
ORDER BY ordinal_position;

-- Check the structure of dssmdobjdepn table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'dssmdobjdepn'
ORDER BY ordinal_position;

-- =====================================================================================
-- STEP 2: SAMPLE DATA FROM TABLES
-- =====================================================================================

-- Sample data from dssmdobjinfo
SELECT *
FROM dssmdobjinfo
LIMIT 10;

-- Sample data from dssmdobjdepn
SELECT *
FROM dssmdobjdepn
LIMIT 10;

-- =====================================================================================
-- STEP 3: SEARCH FOR REPORT OBJECTS
-- =====================================================================================

-- Look for any report objects (object_type = 3)
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype,
    date_created
FROM dssmdobjinfo
WHERE object_type = 3
ORDER BY object_name
LIMIT 20;

-- Look for objects with names containing 'CS0938327' or 'Report'
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype,
    date_created
FROM dssmdobjinfo
WHERE object_name ILIKE '%CS0938327%'
   OR object_name ILIKE '%Report%'
   OR object_name ILIKE '%FFReport%'
ORDER BY object_name;

-- =====================================================================================
-- STEP 4: SEARCH FOR METRIC OBJECTS
-- =====================================================================================

-- Look for metric objects (object_type = 4)
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype,
    date_created
FROM dssmdobjinfo
WHERE object_type = 4
ORDER BY object_name
LIMIT 20;

-- =====================================================================================
-- STEP 5: EXAMINE OBJECT TYPES AND SUBTYPES
-- =====================================================================================

-- Get a summary of all object types and subtypes in the database
SELECT 
    object_type,
    object_subtype,
    COUNT(*) as object_count,
    MIN(object_name) as sample_name
FROM dssmdobjinfo
GROUP BY object_type, object_subtype
ORDER BY object_type, object_subtype;

-- =====================================================================================
-- STEP 6: TEST DEPENDENCY RELATIONSHIPS
-- =====================================================================================

-- Sample dependency relationships
SELECT 
    dep.parent_object_id,
    parent_obj.object_name as parent_name,
    parent_obj.object_type as parent_type,
    dep.child_object_id,
    child_obj.object_name as child_name,
    child_obj.object_type as child_type
FROM dssmdobjdepn dep
INNER JOIN dssmdobjinfo parent_obj ON dep.parent_object_id = parent_obj.object_id
INNER JOIN dssmdobjinfo child_obj ON dep.child_object_id = child_obj.object_id
LIMIT 20;

-- =====================================================================================
-- STEP 7: ALTERNATIVE SEARCH PATTERNS
-- =====================================================================================

-- If the exact report name doesn't exist, try variations
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype
FROM dssmdobjinfo
WHERE object_name ILIKE '%CS0938327%'
   OR object_name ILIKE '%938327%'
   OR object_name ILIKE '%FFReport%'
   OR object_name ILIKE '%Report2%'
ORDER BY object_name;

-- Look for objects with similar patterns
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype
FROM dssmdobjinfo
WHERE object_name ~ '^CS[0-9]+.*Report.*$'
   OR object_name ~ '.*Report[0-9]+$'
ORDER BY object_name;

-- =====================================================================================
-- EXECUTION INSTRUCTIONS:
-- =====================================================================================
-- 1. Run Step 1 to verify table structures match expected columns
-- 2. Run Step 2 to see sample data and understand the data format
-- 3. Run Steps 3-4 to find report and metric objects
-- 4. Run Step 5 to understand all object types in your database
-- 5. Run Step 6 to verify dependency relationships work
-- 6. Run Step 7 if the exact report name is not found
-- 
-- Expected columns in dssmdobjinfo:
-- - object_id (UUID/GUID)
-- - object_name (VARCHAR)
-- - object_type (INTEGER)
-- - object_subtype (INTEGER)
-- - date_created, date_modified (TIMESTAMP)
--
-- Expected columns in dssmdobjdepn:
-- - parent_object_id (UUID/GUID)
-- - child_object_id (UUID/GUID)
-- - dependency_type (INTEGER, optional)
-- =====================================================================================
