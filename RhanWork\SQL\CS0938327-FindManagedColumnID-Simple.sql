-- Simple Query to Find Column Dependencies for Metrics in Report: CS0938327FFReport2
-- Database: poc_metadata (SQL Server)
-- Report: CS0938327FFReport2

-- Quick Query: Get all column objects depended by metrics in the report
WITH report_metrics AS (
    -- Get all metrics used in the specified report
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM dssobjects rep
    INNER JOIN dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssobjects met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3      -- Report object type
        AND rep.object_subtype = 768 -- Report subtype
        AND met.object_type = 4      -- Metric object type
        AND met.object_subtype = 1024 -- Metric subtype
),
column_dependencies AS (
    -- Get all column dependencies for each metric
    SELECT DISTINCT
        rm.metric_id,
        rm.metric_name,
        dep.child_object_id as column_id,
        col.object_name as column_name,
        col.object_type,
        col.object_subtype
    FROM report_metrics rm
    INNER JOIN dssobjdep dep ON rm.metric_id = dep.parent_object_id
    INNER JOIN dssobjects col ON dep.child_object_id = col.object_id
    WHERE col.object_type IN (12, 13) -- Column and Attribute object types
)
SELECT 
    cd.metric_name,
    cd.column_id,
    cd.column_name,
    cd.object_type,
    cd.object_subtype,
    CASE 
        WHEN cd.object_type = 12 THEN 'Column'
        WHEN cd.object_type = 13 THEN 'Attribute'
        ELSE 'Other'
    END as object_type_description
FROM column_dependencies cd
ORDER BY cd.metric_name, cd.column_name;

-- Alternative query if the above table names don't exist:
-- Replace 'dssobjects' with 'DSSOBJECTS' and 'dssobjdep' with 'DSSOBJDEP' if needed

/*
-- Backup query with different table naming convention
WITH report_metrics AS (
    SELECT DISTINCT
        dep.CHILD_OBJECT_ID as metric_id,
        met.OBJECT_NAME as metric_name
    FROM DSSOBJECTS rep
    INNER JOIN DSSOBJDEP dep ON rep.OBJECT_ID = dep.PARENT_OBJECT_ID
    INNER JOIN DSSOBJECTS met ON dep.CHILD_OBJECT_ID = met.OBJECT_ID
    WHERE rep.OBJECT_NAME = 'CS0938327FFReport2'
        AND rep.OBJECT_TYPE = 3
        AND rep.OBJECT_SUBTYPE = 768
        AND met.OBJECT_TYPE = 4
        AND met.OBJECT_SUBTYPE = 1024
),
column_dependencies AS (
    SELECT DISTINCT
        rm.metric_id,
        rm.metric_name,
        dep.CHILD_OBJECT_ID as column_id,
        col.OBJECT_NAME as column_name,
        col.OBJECT_TYPE,
        col.OBJECT_SUBTYPE
    FROM report_metrics rm
    INNER JOIN DSSOBJDEP dep ON rm.metric_id = dep.PARENT_OBJECT_ID
    INNER JOIN DSSOBJECTS col ON dep.CHILD_OBJECT_ID = col.OBJECT_ID
    WHERE col.OBJECT_TYPE IN (12, 13)
)
SELECT 
    cd.metric_name,
    cd.column_id,
    cd.column_name,
    cd.OBJECT_TYPE,
    cd.OBJECT_SUBTYPE
FROM column_dependencies cd
ORDER BY cd.metric_name, cd.column_name;
*/
