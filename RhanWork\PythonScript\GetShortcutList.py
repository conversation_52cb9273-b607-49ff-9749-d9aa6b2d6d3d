"""
©2025 MicroStrategy. All rights reserved.
Application : 
Program     : List Shortcut Object
Purpose     :
Modification History
When       : Who    : What
2025/x/xx  : MSTR Ikeda : 新規作成
"""
# パッケージ読み込み
import os
import sys
sys.path.append( os.path.join( os.path.dirname(__file__), "." ) )
import logging
import datetime
import argparse
import pandas as pd
import pandas.io.formats.excel
# from openpyxl.styles import Font

# mstrio-api
from mstrio.connection import Connection
from mstrio.object_management import quick_search
from mstrio.types import ExtendedType, ObjectSubTypes, ObjectTypes
from mstrio.object_management.shortcut import get_shortcuts, ShortcutInfoFlags
import mstrio.config

# added by rhan
from mstrio.object_management.search_operations import full_search
from mstrio.types import ObjectTypes




# 環境変数の読み込み(Constants.py)
from Constants import *


# 変数設定
sTime = datetime.datetime.now().strftime( "%Y%m%d%H%M%S" )
sFileName = os.path.splitext( os.path.basename( os.path.basename(__file__) ) )[0]

# ログ設定
logger = logging.getLogger( __name__ )
logger.setLevel( logging.DEBUG )
handler = logging.StreamHandler()

logger.addHandler( handler )
fmt = logging.Formatter( "%(asctime)s - %(funcName)s - %(levelname)s - %(message)s" )
handler.setFormatter( fmt )
logger.propagate = False 

if bUsingLogfile:
    filehandler = logging.FileHandler( f".\\log\\{sFileName}_{sTime}.log" )
    logger.addHandler( filehandler )
    mstrio.config.logger.addHandler( filehandler ) 
    filehandler.setFormatter( fmt )

mstr_base_url = "http://rhanvm2019:8080/MicroStrategyLibrary/"
mstr_username = "administrator"
mstr_password = ""
project_name  = "MicroStrategy Tutorial"

# Specify the type of object you're interested in, e.g., shortcuts
def list_objects_by_type(conn, obj_type, project_id=None):
    objects = full_search(connection=conn, project=project_id, object_types=[obj_type])
    return objects
    
def main():

    logger.info( f"tool version: 00.")

    #　コマンドライン引数
    parser = argparse.ArgumentParser()
    parser.add_argument( "rootFolderId", type = str, help = "Root Folder GUID" )
    parser.add_argument( "projName", type = str, help = "Project Name" )

    if bUsingCommandLine:
        args = parser.parse_args()
    else:
        args = parser.parse_args(args=["B7CA92F04B9FAE8D941C3E9B7E0CD754",
                                       "MicroStrategy Tutorial",
                                       #"",
                                        ])
        
     
    # ログイン
    if len( args.projName ) > 0:
        project_name = args.projName
    else:
        project_name = mstr_project_name
    
    logger.info( f"Project name is {project_name}." )

    mstr_conn = Connection( base_url = mstr_base_url, 
                            username = mstr_username,
                            password = mstr_password,
                            project_name = project_name,
                            ssl_verify = bVerify,
                            )

    ### added by rhan ---> Begin
    # Searching for Shortcuts
    project_id = "B7CA92F04B9FAE8D941C3E9B7E0CD754"
    shortcuts = list_objects_by_type(mstr_conn, ObjectTypes.SHORTCUT_TYPE, project_id)

    logger.debug(len(shortcuts))
    
    shortcut_ids = []
    i = 0
    for obj in shortcuts:
        if i < 3:
            logger.info(f"{i} --->Name: {obj['name']}, ID: {obj['id']}, owner: {obj['owner']['name']}")
            shortcut_ids.append(obj['id'])
            i = i + 1
            
    ### added by rhan ---> End
            
    logger.debug(mstr_conn.project_name)
    #sToken = mstr_conn._session.headers["X-MSTR-AuthToken"]
    #sSessionId = mstr_conn._session.cookies["JSESSIONID"]

    # ショートカットオブジェクト一覧情報取得
    logger.info( f"*** Read Shortcut Object Info ***" )
    logger.info(f"shortcut_ids --->{shortcut_ids}")
    
    shortcuts = get_shortcuts(  connection = mstr_conn,
                                shortcut_ids = shortcut_ids,
                                project_id = mstr_conn.project_id,
                                #shortcut_info_flag= ShortcutInfoFlags.DssDashboardShortcutInfoTOC,
                                limit = 100000,
                            )
    
    logger.debug(len(shortcuts))
    
    for shortcut in shortcuts:
        logger.debug(shortcut.id)

    mstr_conn.close()






        
if __name__=="__main__":
    main()