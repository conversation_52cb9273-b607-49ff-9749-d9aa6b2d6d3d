#!/bin/bash
# =====================================================================================
# Shell Script: Get Managed Columns for MicroStrategy Report
# Usage: ./get_managed_columns.sh "ReportName"
# Example: ./get_managed_columns.sh "CS0938327FFReport2"
# =====================================================================================

# Database connection parameters
HOST="************"
PORT="5432"
USER="mstr"
DATABASE="poc_metadata"
export PGPASSWORD="zJ7MhP8mdRCI"
export PGCLIENTENCODING="UTF8"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_info() {
    echo -e "${BLUE}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

# Check if report name parameter is provided
if [ $# -eq 0 ]; then
    echo
    print_error "Report name parameter is required!"
    echo
    echo "Usage: $0 \"ReportName\""
    echo "Example: $0 \"CS0938327FFReport2\""
    echo
    exit 1
fi

REPORT_NAME="$1"

echo "====================================================================================="
print_info "MicroStrategy Managed Column Analysis"
echo "====================================================================================="
echo "Report Name: $REPORT_NAME"
echo "Database: $DATABASE on $HOST:$PORT"
echo "User: $USER"
echo

# Test database connection
print_info "Testing database connection..."
if psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -c "SELECT 1;" >/dev/null 2>&1; then
    print_success "Database connection successful!"
else
    print_error "Cannot connect to database!"
    echo "Please check:"
    echo "- Database server is running"
    echo "- Network connectivity"
    echo "- Credentials are correct"
    exit 1
fi
echo

# Create the SQL query with the report name parameter
SQL_QUERY="SELECT DISTINCT 
    r.object_name as report_name,
    r.object_id as report_id,
    m.object_name as metric_name,
    m.object_id as metric_id,
    mc.object_name as managed_column_name,
    mc.object_id as managed_column_id
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d1 ON r.object_id = d1.object_id
INNER JOIN dssmdobjinfo m ON d1.depn_objid = m.object_id
INNER JOIN dssmdobjdepn d2 ON m.object_id = d2.object_id
INNER JOIN dssmdobjinfo mc ON d2.depn_objid = mc.object_id
WHERE r.object_name = '$REPORT_NAME'
    AND r.object_type = 3 AND r.subtype = 768
    AND m.object_type = 4 AND m.subtype = 1024
    AND mc.object_type = 26 AND mc.subtype = 6656 AND mc.extended_type = 3
ORDER BY m.object_name, mc.object_name;"

print_info "Executing query for report: $REPORT_NAME"
echo "====================================================================================="

# Execute the query
if ! psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -c "$SQL_QUERY" 2>/tmp/error.log; then
    echo
    print_error "Query execution failed!"
    echo "Error details:"
    cat /tmp/error.log
    rm -f /tmp/error.log
    exit 1
fi

# Clean up temporary files
rm -f /tmp/error.log

echo
echo "====================================================================================="
print_success "Analysis completed successfully!"
echo
print_info "Column descriptions:"
echo "- report_name: Name of the MicroStrategy report"
echo "- report_id: Unique identifier of the report"
echo "- metric_name: Name of the metric within the report"
echo "- metric_id: Unique identifier of the metric"
echo "- managed_column_name: Name of the managed column used by the metric"
echo "- managed_column_id: Unique identifier of the managed column"
echo "====================================================================================="

# Ask if user wants to save results to file
echo
read -p "Save results to file? (y/n): " SAVE_TO_FILE
if [[ "$SAVE_TO_FILE" =~ ^[Yy]$ ]]; then
    # Create filename with timestamp
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    OUTPUT_FILE="managed_columns_${REPORT_NAME}_${TIMESTAMP}.csv"
    # Remove spaces and special characters from filename
    OUTPUT_FILE=$(echo "$OUTPUT_FILE" | tr ' ' '_' | tr -d '[:punct:]' | tr -d '[:space:]')
    OUTPUT_FILE="${OUTPUT_FILE}.csv"
    
    echo
    print_info "Saving results to: $OUTPUT_FILE"
    
    # Create CSV header
    echo "report_name,report_id,metric_name,metric_id,managed_column_name,managed_column_id" > "$OUTPUT_FILE"
    
    # Execute query with CSV format
    psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -c "COPY ($SQL_QUERY) TO STDOUT WITH CSV;" >> "$OUTPUT_FILE"
    
    print_success "Results saved to: $OUTPUT_FILE"
fi

echo
