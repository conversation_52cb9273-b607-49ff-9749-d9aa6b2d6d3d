from enum import Enum


class PredefinedFolders(Enum):
    """Enumeration constants used to specify names of pre-defined folders.
    Values are specified according to `EnumDSSXMLFolderNames`."""

    AUTO_STYLES = 57
    BLACK_LISTED = 1000
    CONFIGURE_DB_ROLES = 73
    CONFIGURE_MONITORS = 58
    CONFIGURE_SERVER_DEFINITIONS = 59
    DB_CONNECTIONS = 81
    DB_LOGINS = 82
    DBMS = 76
    DEVICES = 88
    EVENTS = 72
    LINKS = 83
    LOCALES = 74
    MY_DASHBOARDS = 96
    MY_SHARED_DASHBOARDS = 97
    PALETTES = 94
    PROFILE_ANSWERS = 21
    PROFILE_FAVORITES = 22
    PROFILE_OBJECTS = 19
    PROFILE_OTHER = 23
    PROFILE_REPORTS = 20
    PROFILE_SEGMENTS = 92
    PROJECTS = 77
    PROPERTY_SETS = 75
    PUBLIC_CONSOLIDATIONS = 2
    PUBLIC_CUSTOM_GROUPS = 3
    PUBLIC_FILTERS = 4
    PUBLIC_METRICS = 5
    PUBLIC_OBJECTS = 1
    PUBLIC_PROMPTS = 6
    PUBLIC_REPORTS = 7
    PUBLIC_SEARCHES = 8
    PUBLIC_TEMPLATES = 9
    ROOT = 39
    SCHEDULE_OBJECTS = 84
    SCHEDULE_TRIGGERS = 85
    SCHEMA_ARITHMETIC_OPERATORS = 49
    SCHEMA_ATTRIBUTE_FORMS = 25
    SCHEMA_ATTRIBUTES = 26
    SCHEMA_BASIC_FUNCTIONS = 41
    SCHEMA_COLUMNS = 27
    SCHEMA_COMPARISON_FOR_RANK_OPERATORS = 51
    SCHEMA_COMPARISON_OPERATORS = 50
    SCHEMA_DATA_EXPLORER = 28
    SCHEMA_DATE_AND_TIME_FUNCTIONS = 42
    SCHEMA_FACTS = 29
    SCHEMA_FINANCIAL_FUNCTIONS = 54
    SCHEMA_FUNCTIONS = 30
    SCHEMA_FUNCTIONS_NESTED = 40
    SCHEMA_HIERARCHIES = 31
    SCHEMA_INTERNAL_FUNCTIONS = 43
    SCHEMA_LOGICAL_OPERATORS = 52
    SCHEMA_MATH_FUNCTIONS = 55
    SCHEMA_NULL_ZERO_FUNCTIONS = 44
    SCHEMA_OBJECTS = 24
    SCHEMA_OLAP_FUNCTIONS = 45
    SCHEMA_OPERATORS = 48
    SCHEMA_PARTITION_FILTERS = 32
    SCHEMA_PARTITION_MAPPINGS = 33
    SCHEMA_PLUG_IN_PACKAGES = 53
    SCHEMA_RANK_AND_N_TILE_FUNCTIONS = 46
    SCHEMA_STATISTICAL_FUNCTIONS = 56
    SCHEMA_STRING_FUNCTIONS = 47
    SCHEMA_SUBTOTALS = 34
    SCHEMA_TABLES = 35
    SCHEMA_TRANSFORMATION_ATTRIBUTES = 37
    SCHEMA_TRANSFORMATIONS = 38
    SCHEMA_WAREHOUSE_TABLES = 36
    SECURITY_ROLES = 80
    SYSTEM_DIMENSION = 91
    SYSTEM_DRILL_MAP = 68
    SYSTEM_DUMMY_PARTITION_TABLES = 70
    SYSTEM_LINKS = 62
    SYSTEM_MD_SECURITY_FILTERS = 69
    SYSTEM_OBJECTS = 61
    SYSTEM_PARSER_FOLDER = 64
    SYSTEM_PROPERTY_SETS = 63
    SYSTEM_SCHEMA_FOLDER = 65
    SYSTEM_SYSTEM_HIERARCHY = 67
    SYSTEM_SYSTEM_PROMPTS = 71
    SYSTEM_WAREHOUSE_CATALOG = 66
    TABLE_SOURCES = 86
    TEMPLATE_ANALYSIS = 93
    TEMPLATE_CONSOLIDATIONS = 11
    TEMPLATE_CUSTOM_GROUPS = 12
    TEMPLATE_DASHBOARDS = 90
    TEMPLATE_DOCUMENTS = 60
    TEMPLATE_FILTERS = 13
    TEMPLATE_METRICS = 14
    TEMPLATE_OBJECTS = 10
    TEMPLATE_PROMPTS = 15
    TEMPLATE_REPORTS = 16
    TEMPLATE_SEARCHES = 17
    TEMPLATE_TEMPLATES = 18
    THEMES = 95
    TRANSMITTERS = 89
    USER_GROUPS = 79
    USERS = 78
    VERSION_UPDATE_HISTORY = 87
