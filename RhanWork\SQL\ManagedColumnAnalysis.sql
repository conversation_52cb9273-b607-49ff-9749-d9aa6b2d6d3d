-- Comprehensive Analysis: Find Managed Column Objects for Report CS0938327FFReport2
-- Database: poc_metadata (PostgreSQL) - Server: 10.27.72.202:5432
-- Target: Find managed columns (object_type = 26, subtype = 6656, extended_type = 3)

-- =====================================================================================
-- STEP 1: VERIFY REPORT AND GET METRICS
-- =====================================================================================

-- Verify the report exists
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    create_time
FROM dssmdobjinfo 
WHERE object_name = 'CS0938327FFReport2'
    AND object_type = 3
    AND subtype = 768;

-- Get all metrics in the report
SELECT DISTINCT
    m.object_id as metric_id,
    m.object_name as metric_name,
    m.object_type,
    m.subtype
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
WHERE r.object_name = 'CS0938327FFReport2'
    AND r.object_type = 3
    AND r.subtype = 768
    AND m.object_type = 4
    AND m.subtype = 1024
ORDER BY m.object_name;

-- =====================================================================================
-- STEP 2: FIND MANAGED COLUMNS (TYPE 26)
-- =====================================================================================

-- Look for managed column objects in the entire database
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    extended_type,
    description,
    create_time,
    parent_id
FROM dssmdobjinfo
WHERE object_type = 26  -- Managed column type
ORDER BY object_name;

-- Check if subtype 6656 (0x1A00) exists
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    extended_type,
    description
FROM dssmdobjinfo
WHERE object_type = 26 
    AND subtype = 6656  -- 0x1A00
ORDER BY object_name;

-- Check if extended_type 3 exists
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    extended_type,
    description
FROM dssmdobjinfo
WHERE object_type = 26 
    AND extended_type = 3
ORDER BY object_name;

-- =====================================================================================
-- STEP 3: FIND MANAGED COLUMNS USED BY METRICS
-- =====================================================================================

-- Direct dependencies: Find managed columns directly used by metrics
WITH report_metrics AS (
    SELECT DISTINCT
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3 AND r.subtype = 768
        AND m.object_type = 4 AND m.subtype = 1024
)
SELECT DISTINCT
    rm.metric_name,
    mc.object_id as managed_column_id,
    mc.object_name as managed_column_name,
    mc.object_type,
    mc.subtype,
    mc.extended_type,
    mc.description,
    mc.create_time,
    mc.parent_id
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo mc ON d.depn_objid = mc.object_id
WHERE mc.object_type = 26  -- Managed columns
ORDER BY rm.metric_name, mc.object_name;

-- =====================================================================================
-- STEP 4: RECURSIVE SEARCH FOR MANAGED COLUMNS
-- =====================================================================================

-- Recursively search through all dependencies to find managed columns
WITH RECURSIVE metric_dependencies AS (
    -- Base case: Start with metrics from the report
    SELECT 
        m.object_id as source_metric_id,
        m.object_name as source_metric_name,
        m.object_id as current_object_id,
        m.object_name as current_object_name,
        m.object_type as current_object_type,
        m.subtype as current_subtype,
        m.extended_type as current_extended_type,
        0 as depth_level,
        ARRAY[m.object_id] as path
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3 AND r.subtype = 768
        AND m.object_type = 4 AND m.subtype = 1024
    
    UNION ALL
    
    -- Recursive case: Follow dependencies
    SELECT 
        md.source_metric_id,
        md.source_metric_name,
        c.object_id,
        c.object_name,
        c.object_type,
        c.subtype,
        c.extended_type,
        md.depth_level + 1,
        md.path || c.object_id
    FROM metric_dependencies md
    INNER JOIN dssmdobjdepn d ON md.current_object_id = d.object_id
    INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
    WHERE md.depth_level < 5  -- Limit recursion depth
        AND NOT (c.object_id = ANY(md.path))  -- Prevent cycles
)
SELECT DISTINCT
    md.source_metric_name,
    md.current_object_id as object_id,
    md.current_object_name as object_name,
    md.current_object_type as object_type,
    md.current_subtype as subtype,
    md.current_extended_type as extended_type,
    md.depth_level
FROM metric_dependencies md
WHERE md.current_object_type = 26  -- Focus on managed columns
ORDER BY md.source_metric_name, md.depth_level, md.current_object_name;

-- =====================================================================================
-- STEP 5: ANALYZE ALL OBJECT TYPES TO UNDERSTAND THE STRUCTURE
-- =====================================================================================

-- Get summary of all object types
SELECT 
    object_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN subtype = 6656 THEN 1 END) as subtype_6656_count,
    COUNT(CASE WHEN extended_type = 3 THEN 1 END) as extended_type_3_count
FROM dssmdobjinfo
GROUP BY object_type
ORDER BY object_type;

-- =====================================================================================
-- STEP 6: DETAILED MANAGED COLUMN INFORMATION
-- =====================================================================================

-- Get detailed information for all managed columns found
WITH all_managed_columns AS (
    -- Get managed columns from recursive search
    WITH RECURSIVE metric_dependencies AS (
        SELECT 
            m.object_id as source_metric_id,
            m.object_name as source_metric_name,
            m.object_id as current_object_id,
            0 as depth_level,
            ARRAY[m.object_id] as path
        FROM dssmdobjinfo r
        INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
        INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
        WHERE r.object_name = 'CS0938327FFReport2'
            AND r.object_type = 3 AND r.subtype = 768
            AND m.object_type = 4 AND m.subtype = 1024
        
        UNION ALL
        
        SELECT 
            md.source_metric_id,
            md.source_metric_name,
            c.object_id,
            md.depth_level + 1,
            md.path || c.object_id
        FROM metric_dependencies md
        INNER JOIN dssmdobjdepn d ON md.current_object_id = d.object_id
        INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
        WHERE md.depth_level < 5
            AND NOT (c.object_id = ANY(md.path))
    )
    SELECT DISTINCT md.current_object_id as managed_column_id
    FROM metric_dependencies md
    INNER JOIN dssmdobjinfo obj ON md.current_object_id = obj.object_id
    WHERE obj.object_type = 26
)
SELECT 
    mc.object_id,
    mc.object_name,
    mc.object_type,
    mc.subtype,
    mc.extended_type,
    mc.description,
    mc.abbreviation,
    mc.create_time,
    mc.mod_time,
    mc.parent_id,
    mc.owner_id,
    mc.object_state,
    mc.hidden,
    -- Try to find parent object information
    parent.object_name as parent_name,
    parent.object_type as parent_type
FROM dssmdobjinfo mc
LEFT JOIN dssmdobjinfo parent ON mc.parent_id = parent.object_id
WHERE mc.object_id IN (SELECT managed_column_id FROM all_managed_columns)
    OR mc.object_type = 26  -- Include all managed columns for reference
ORDER BY mc.object_name;

-- =====================================================================================
-- STEP 7: VERIFY SPECIFIC EXAMPLE FROM REQUIREMENTS
-- =====================================================================================

-- Verify the specific example mentioned in requirements
-- Metric: IJ期初金額＿前期1年累計 (ID: 3FCDB9CA41EB46AAAFD692528B54AA72)
-- Managed Column: IJ期初金額＿前期1年累計 (ID: 5079544DF950476FBD5833798EDCE295)

SELECT 
    'METRIC' as object_role,
    object_id,
    object_name,
    object_type,
    subtype,
    extended_type
FROM dssmdobjinfo
WHERE object_id = '3FCDB9CA41EB46AAAFD692528B54AA72'

UNION ALL

SELECT 
    'MANAGED_COLUMN' as object_role,
    object_id,
    object_name,
    object_type,
    subtype,
    extended_type
FROM dssmdobjinfo
WHERE object_id = '5079544DF950476FBD5833798EDCE295';

-- Check dependency between the specific metric and managed column
SELECT 
    d.object_id as parent_id,
    d.depn_objid as child_id,
    p.object_name as parent_name,
    c.object_name as child_name,
    c.object_type as child_type
FROM dssmdobjdepn d
INNER JOIN dssmdobjinfo p ON d.object_id = p.object_id
INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
WHERE d.object_id = '3FCDB9CA41EB46AAAFD692528B54AA72'
    AND d.depn_objid = '5079544DF950476FBD5833798EDCE295';

-- =====================================================================================
-- EXECUTION NOTES:
-- =====================================================================================
-- This comprehensive analysis will:
-- 1. Verify the report and get its metrics
-- 2. Find all managed column objects (type 26)
-- 3. Find managed columns directly used by metrics
-- 4. Recursively search for managed columns in dependency chain
-- 5. Analyze object type distribution
-- 6. Get detailed information for all found managed columns
-- 7. Verify the specific example from requirements
-- =====================================================================================
