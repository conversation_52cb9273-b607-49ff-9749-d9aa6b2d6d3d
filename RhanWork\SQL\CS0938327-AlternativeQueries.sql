-- Alternative Queries for Finding Column Dependencies
-- When standard MicroStrategy metadata tables are not available
-- Database: poc_metadata (PostgreSQL)

-- =====================================================================================
-- APPROACH 1: USING INFORMATION_SCHEMA TO FIND REPORT-RELATED DATA
-- =====================================================================================

-- Query 1: Search for report data in any table containing the report name
DO $$
DECLARE
    rec RECORD;
    query_text TEXT;
BEGIN
    FOR rec IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
    LOOP
        BEGIN
            query_text := format('SELECT ''%s'' as table_name, * FROM %I WHERE %I::text ILIKE ''%%CS0938327%%'' LIMIT 5', 
                                rec.table_name, rec.table_name, 
                                (SELECT column_name FROM information_schema.columns 
                                 WHERE table_name = rec.table_name AND table_schema = 'public' 
                                 LIMIT 1));
            EXECUTE query_text;
        EXCEPTION WHEN OTHERS THEN
            -- Skip tables that cause errors
            NULL;
        END;
    END LOOP;
END $$;

-- =====================================================================================
-- APPROACH 2: DIRECT SEARCH IN COMMON TABLE PATTERNS
-- =====================================================================================

-- Query 2: Try common MicroStrategy table name variations
-- (Uncomment and try each one)

-- Option A: Standard lowercase
-- SELECT * FROM dssobjects WHERE object_name = 'CS0938327FFReport2' LIMIT 5;

-- Option B: Uppercase
-- SELECT * FROM DSSOBJECTS WHERE OBJECT_NAME = 'CS0938327FFReport2' LIMIT 5;

-- Option C: Mixed case
-- SELECT * FROM DssObjects WHERE ObjectName = 'CS0938327FFReport2' LIMIT 5;

-- Option D: With underscores
-- SELECT * FROM dss_objects WHERE object_name = 'CS0938327FFReport2' LIMIT 5;

-- Option E: Alternative naming
-- SELECT * FROM mstr_objects WHERE object_name = 'CS0938327FFReport2' LIMIT 5;
-- SELECT * FROM metadata_objects WHERE object_name = 'CS0938327FFReport2' LIMIT 5;
-- SELECT * FROM objects WHERE object_name = 'CS0938327FFReport2' LIMIT 5;

-- =====================================================================================
-- APPROACH 3: USING JSON DATA (Based on your CS0884784_report.json)
-- =====================================================================================

-- Query 3: If you have JSON data stored in tables, search for metric and column info
-- This approach uses the structure from your JSON file

-- Search for tables that might contain JSON data
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND (data_type = 'json' OR data_type = 'jsonb' OR data_type = 'text')
ORDER BY table_name, column_name;

-- =====================================================================================
-- APPROACH 4: SEARCH BY CONTENT PATTERNS
-- =====================================================================================

-- Query 4: Search for specific metric and column IDs from your JSON
-- Based on the JSON file, look for these specific IDs:

-- Metric ID: EA939629597A4FB09D0D57F3D4734F2A (数＿一括見込)
-- Column IDs from JSON:
-- - 3AC02B8D7F0743C1AF94A0D404DEA65E (部門CD)
-- - B4BA86912CC2493C9DFF02E7ED1BF799 (部門名)
-- - F6458896FC604F87AE35D7EE0715F999 (数)

-- Search for these IDs in any text/varchar columns
DO $$
DECLARE
    rec RECORD;
    col_rec RECORD;
    query_text TEXT;
    search_ids TEXT[] := ARRAY[
        'EA939629597A4FB09D0D57F3D4734F2A',
        '3AC02B8D7F0743C1AF94A0D404DEA65E', 
        'B4BA86912CC2493C9DFF02E7ED1BF799',
        'F6458896FC604F87AE35D7EE0715F999'
    ];
    search_id TEXT;
BEGIN
    FOR rec IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
    LOOP
        FOR col_rec IN
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = rec.table_name 
                AND table_schema = 'public'
                AND data_type IN ('text', 'character varying', 'varchar', 'char')
        LOOP
            FOREACH search_id IN ARRAY search_ids
            LOOP
                BEGIN
                    query_text := format('SELECT ''%s'' as table_name, ''%s'' as column_name, ''%s'' as search_id, %I FROM %I WHERE %I ILIKE ''%%%s%%'' LIMIT 3', 
                                        rec.table_name, col_rec.column_name, search_id,
                                        col_rec.column_name, rec.table_name, col_rec.column_name, search_id);
                    EXECUTE query_text;
                EXCEPTION WHEN OTHERS THEN
                    NULL;
                END;
            END LOOP;
        END LOOP;
    END LOOP;
END $$;

-- =====================================================================================
-- APPROACH 5: MANUAL TABLE STRUCTURE ANALYSIS
-- =====================================================================================

-- Query 5: Analyze table structures to find object-like tables
SELECT 
    t.table_name,
    COUNT(c.column_name) as column_count,
    STRING_AGG(c.column_name, ', ' ORDER BY c.ordinal_position) as columns
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
WHERE t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
GROUP BY t.table_name
HAVING COUNT(c.column_name) > 3  -- Focus on tables with multiple columns
ORDER BY column_count DESC;

-- Query 6: Look for tables with ID-like columns
SELECT DISTINCT
    table_name,
    column_name
FROM information_schema.columns
WHERE table_schema = 'public'
    AND (
        column_name ILIKE '%id%' 
        OR column_name ILIKE '%object%'
        OR column_name ILIKE '%name%'
        OR column_name ILIKE '%type%'
    )
ORDER BY table_name, column_name;

-- =====================================================================================
-- INSTRUCTIONS FOR EXECUTION:
-- =====================================================================================
-- 1. Start with the diagnostic queries file first
-- 2. Run Query 5 and 6 here to understand your table structure
-- 3. Try the specific table name variations in Query 2
-- 4. Use the JSON-based approach if your data is stored as JSON
-- 5. Use the content search approach (Query 4) to find specific IDs
-- 6. Once you identify the correct tables, modify the main query file accordingly
-- =====================================================================================
