-- PostgreSQL version of #31AS＿直販IJ孔版ABCD顧客分類 view
-- Converted from Teradata SQL
-- Note: View name simplified to remove special characters for PostgreSQL compatibility

CREATE VIEW "31AS_直販IJ孔版ABCD顧客分類"(
  "顧客事業所コード"
 ,"顧客コード"
 ,"顧客名"
 ,"郵便番号"
 ,"住所"
 ,"期"
 ,"年前"
 ,"新規"
 ,"IJ期初ABCD"
 ,"IJ5月ABCD_12ヵ月移動累計"
 ,"IJ6月ABCD_12ヵ月移動累計"
 ,"IJ7月ABCD_12ヵ月移動累計"
 ,"IJ8月ABCD_12ヵ月移動累計"
 ,"IJ9月ABCD_12ヵ月移動累計"
 ,"IJ10月ABCD_12ヵ月移動累計"
 ,"IJ11月ABCD_12ヵ月移動累計"
 ,"IJ12月ABCD_12ヵ月移動累計"
 ,"IJ1月ABCD_12ヵ月移動累計"
 ,"IJ2月ABCD_12ヵ月移動累計"
 ,"IJ3月ABCD_12ヵ月移動累計"
 ,"IJ期初金額_前期1年累計"
 ,"IJ期初本数_前期1年累計"
 ,"IJ期初本数_1000ml換算_前期1年"
 ,"RG期初ABCD"
 ,"RG5月ABCD_12ヵ月移動累計"
 ,"RG6月ABCD_12ヵ月移動累計"
 ,"RG7月ABCD_12ヵ月移動累計"
 ,"RG8月ABCD_12ヵ月移動累計"
 ,"RG9月ABCD_12ヵ月移動累計"
 ,"RG10月ABCD_12ヵ月移動累計"
 ,"RG11月ABCD_12ヵ月移動累計"
 ,"RG12月ABCD_12ヵ月移動累計"
 ,"RG1月ABCD_12ヵ月移動累計"
 ,"RG2月ABCD_12ヵ月移動累計"
 ,"RG3月ABCD_12ヵ月移動累計"
 ,"RG期初金額_前期1年累計"
 ,"IJ直近ABCD"
 ,"IJ直近金額_12ヵ月移動累計"
 ,"IJ直近本数_12ヵ月移動累計"
 ,"IJ直近本数_1000ml換算_12ヵ月"
 ,"RG直近ABCD"
 ,"RG直近金額_12ヵ月移動累計"
)
AS SELECT 
  V_CODE_KOKYAKUJIGYOSYO
 ,V_CODE_KOKYAKU
 ,V_KANJ_KOKYAKUMEI
 ,V_CODE_YUBIN
 ,V_KANJ_JYUSHO
 ,V_DATE_URIAGE_KI
 ,V_DATE_NEN
 ,V_KBN_NEWJG
 ,V_NUM_IJ_ABCD04
 ,V_NUM_IJ_ABCD05
 ,V_NUM_IJ_ABCD06
 ,V_NUM_IJ_ABCD07
 ,V_NUM_IJ_ABCD08
 ,V_NUM_IJ_ABCD09
 ,V_NUM_IJ_ABCD10
 ,V_NUM_IJ_ABCD11
 ,V_NUM_IJ_ABCD12
 ,V_NUM_IJ_ABCD01
 ,V_NUM_IJ_ABCD02
 ,V_NUM_IJ_ABCD03
 ,V_NUM_IJ_KINGAKU
 ,V_NUM_IJ_SURYO
 ,V_NUM_IJ_SURYO_1000
 ,V_NUM_RG_ABCD04
 ,V_NUM_RG_ABCD05
 ,V_NUM_RG_ABCD06
 ,V_NUM_RG_ABCD07
 ,V_NUM_RG_ABCD08
 ,V_NUM_RG_ABCD09
 ,V_NUM_RG_ABCD10
 ,V_NUM_RG_ABCD11
 ,V_NUM_RG_ABCD12
 ,V_NUM_RG_ABCD01
 ,V_NUM_RG_ABCD02
 ,V_NUM_RG_ABCD03
 ,V_NUM_RG_KINGAKU
 ,V_NUM_IJ_ABCD_NOW
 ,V_NUM_IJ_KINGAKU_NOW
 ,V_NUM_IJ_SURYO_NOW
 ,V_NUM_IJ_SURYO_1000_NOW
 ,V_NUM_RG_ABCD_NOW
 ,V_NUM_RG_KINGAKU_NOW
FROM
  V_S_SUPPLY_ABCD_KI;

-- Optional: Add comment for documentation
COMMENT ON VIEW bi."31AS_直販IJ孔版ABCD顧客分類" IS 'Direct sales IJ stencil ABCD customer classification view - converted from Teradata';
