2024-11-11 18:10:01,822 - main - INFO - Number of rows of input data is 1.
2024-11-11 18:10:04,348 - main - INFO - Status is True.
2024-11-11 18:10:04,358 - main - INFO - Target object id = [E47D7CA64FE6A207A2A814A8F1124656], Node Key = [W68], output file = [test_small.csv]
2024-11-11 18:10:04,358 - main - INFO - Get Document Instance Id.
2024-11-11 18:10:05,188 - main - DEBUG - Document Instance id is 7459C778426BD3E92B0980A6B8D6F771.
2024-11-11 18:10:05,188 - main - INFO - Export visualization data to csv.
2024-11-11 18:10:06,328 - main - DEBUG - 200
2024-11-11 18:10:06,331 - main - INFO - Save to test_small.csv.
2024-11-11 18:10:06,331 - main - INFO - Copy a file to network shared folder.
2024-11-11 18:10:06,331 - main - INFO - Successfully changed attribute for now[0].
2024-11-11 18:10:06,331 - main - INFO - Number of successful transactions: 1
2024-11-11 18:10:06,331 - main - INFO - Number of failed transactions: 0
2024-11-11 18:10:06,695 - main - ERROR - Exception occurred in connection close
Traceback (most recent call last):
  File "C:\00work\20MSTR\00Case\_PythonScript\GetDocumentVizData_v02.py", line 239, in main
    a = 12 / 0
        ~~~^~~
ZeroDivisionError: division by zero
