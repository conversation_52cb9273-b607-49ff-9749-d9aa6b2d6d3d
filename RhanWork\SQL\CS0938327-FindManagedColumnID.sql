-- DatabaseInfo:
-- server ip: ************
-- server port: 1433
-- server name: RhanVM2019
-- database name: poc_metadata
-- schema: public
-- username: mstr
-- password: zJ7MhP8mdRCI

-- Object Types:
-- metric object_type = 4 subtype = 1024
-- report object_type = 3 subtype = 768
-- managed column object_type = 26 subtype = 0x1A00, ExtType = 0x3

-- Parameter:
-- report name: CS0938327FFReport2

-- For example, the following metric included in report (CS0938327FFReport2), which depends on 1 managed column (object_type = 26, subtype = 0x1A00, ExtType = 0x3)
-- Info of Metric:
--    Name: IJ期初金額＿前期1年累計
--    object_id: 3FCDB9CA41EB46AAAFD692528B54AA72
-- Info of the dependent managed column
--    Name: IJ期初金額＿前期1年累計
--    object_id: 5079544DF950476FBD5833798EDCE295

My purpose is get all managed columns info included in the given report (CS0938327FFReport2),
but you need to find them through all the metric items included in the report.

-- =====================================================================================
-- SOLUTION: FIND ALL MANAGED COLUMNS USED BY METRICS IN THE REPORT
-- OUTPUT FORMAT: report_name | report_id | metric_name | metric_id | managed_column_name | managed_column_id
-- =====================================================================================

-- SIMPLE VERSION: Just change the report name parameter
SELECT DISTINCT
    r.object_name as report_name,
    r.object_id as report_id,
    m.object_name as metric_name,
    m.object_id as metric_id,
    mc.object_name as managed_column_name,
    mc.object_id as managed_column_id
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d1 ON r.object_id = d1.object_id
INNER JOIN dssmdobjinfo m ON d1.depn_objid = m.object_id
INNER JOIN dssmdobjdepn d2 ON m.object_id = d2.object_id
INNER JOIN dssmdobjinfo mc ON d2.depn_objid = mc.object_id
WHERE r.object_name = 'CS0938327FFReport2'  -- *** CHANGE THIS PARAMETER ***
    AND r.object_type = 3 AND r.subtype = 768  -- Report
    AND m.object_type = 4 AND m.subtype = 1024  -- Metric
    AND mc.object_type = 26 AND mc.subtype = 6656 AND mc.extended_type = 3  -- Managed Column
ORDER BY m.object_name, mc.object_name;

-- MAIN QUERY: Get report, metrics, and managed columns in one result set
-- Input parameter: Report name (change 'CS0938327FFReport2' to your target report)
WITH report_info AS (
    SELECT
        r.object_name as report_name,
        r.object_id as report_id
    FROM dssmdobjinfo r
    WHERE r.object_name = 'CS0938327FFReport2'  -- *** CHANGE THIS PARAMETER ***
        AND r.object_type = 3
        AND r.subtype = 768
),
report_metrics AS (
    SELECT DISTINCT
        ri.report_name,
        ri.report_id,
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM report_info ri
    INNER JOIN dssmdobjdepn d ON ri.report_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE m.object_type = 4
        AND m.subtype = 1024
)
SELECT DISTINCT
    rm.report_name,
    rm.report_id,
    rm.metric_name,
    rm.metric_id,
    mc.object_name as managed_column_name,
    mc.object_id as managed_column_id
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo mc ON d.depn_objid = mc.object_id
WHERE mc.object_type = 26  -- Managed columns
    AND mc.subtype = 6656  -- 0x1A00
    AND mc.extended_type = 3
ORDER BY rm.metric_name, mc.object_name;

-- ALTERNATIVE: Generic version with parameter placeholder
-- Replace @ReportName with actual report name when executing
/*
WITH report_info AS (
    SELECT
        r.object_name as report_name,
        r.object_id as report_id
    FROM dssmdobjinfo r
    WHERE r.object_name = @ReportName  -- Parameter: Report name
        AND r.object_type = 3
        AND r.subtype = 768
),
report_metrics AS (
    SELECT DISTINCT
        ri.report_name,
        ri.report_id,
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM report_info ri
    INNER JOIN dssmdobjdepn d ON ri.report_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE m.object_type = 4
        AND m.subtype = 1024
)
SELECT DISTINCT
    rm.report_name,
    rm.report_id,
    rm.metric_name,
    rm.metric_id,
    mc.object_name as managed_column_name,
    mc.object_id as managed_column_id
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo mc ON d.depn_objid = mc.object_id
WHERE mc.object_type = 26  -- Managed columns
    AND mc.subtype = 6656  -- 0x1A00
    AND mc.extended_type = 3
ORDER BY rm.metric_name, mc.object_name;
*/

-- EXPECTED OUTPUT FORMAT:
-- report_name | report_id | metric_name | metric_id | managed_column_name | managed_column_id
-- Example result for CS0938327FFReport2:
-- CS0938327FFReport2 | 0E9F35104A20F9EB83AAC9B7B9C613C5 | IJ期初金額＿前期1年累計 | 3FCDB9CA41EB46AAAFD692528B54AA72 | IJ期初金額＿前期1年累計 | 5079544DF950476FBD5833798EDCE295
-- CS0938327FFReport2 | 0E9F35104A20F9EB83AAC9B7B9C613C5 | IJ期初本数＿前期1年累計 | B2627BE04448440B9AA772CE475D2C2A | IJ期初本数＿前期1年累計 | 6E25F8CA26BC48D0ADBDD366468BE365
