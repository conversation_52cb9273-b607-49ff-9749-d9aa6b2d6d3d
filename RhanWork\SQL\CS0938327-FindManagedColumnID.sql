-- DatabaseInfo:
-- server ip: ************
-- server port: 1433
-- server name: RhanVM2019
-- database name: poc_metadata
-- schema: public
-- username: mstr
-- password: zJ7MhP8mdRCI

-- Object Types:
-- metric object_type = 4 subtype = 1024
-- report object_type = 3 subtype = 768

-- Parameter:
-- report name: CS0938327FFReport2

-- =====================================================================================
-- SQL QUERIES TO FIND ALL COLUMN OBJECTS DEPENDED BY METRIC OBJECTS IN THE REPORT
-- =====================================================================================

-- Query 1: Find the Report Object ID by Name
-- This query locates the specific report object in the metadata
SELECT
    obj.object_id,
    obj.object_name,
    obj.object_type,
    obj.object_subtype,
    obj.date_created,
    obj.date_modified
FROM dssobjects obj
WHERE obj.object_name = 'CS0938327FFReport2'
    AND obj.object_type = 3  -- Report object type
    AND obj.object_subtype = 768;  -- Report subtype

-- Query 2: Find All Metric Objects Used in the Report
-- This query finds all metrics that are used in the specified report
WITH report_metrics AS (
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name,
        met.object_type,
        met.object_subtype
    FROM dssobjects rep
    INNER JOIN dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssobjects met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3  -- Report
        AND rep.object_subtype = 768
        AND met.object_type = 4  -- Metric
        AND met.object_subtype = 1024
)
SELECT * FROM report_metrics;

-- Query 3: Find All Column Dependencies for Metrics in the Report
-- This comprehensive query finds all column objects that the metrics depend on
WITH report_metrics AS (
    -- First, get all metrics used in the report
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM dssobjects rep
    INNER JOIN dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssobjects met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3  -- Report
        AND rep.object_subtype = 768
        AND met.object_type = 4  -- Metric
        AND met.object_subtype = 1024
),
metric_dependencies AS (
    -- Get all dependencies for each metric (recursive dependencies)
    SELECT DISTINCT
        rm.metric_id,
        rm.metric_name,
        dep.child_object_id as dependent_object_id,
        dep.dependency_type
    FROM report_metrics rm
    INNER JOIN dssobjdep dep ON rm.metric_id = dep.parent_object_id
),
column_objects AS (
    -- Filter to get only column objects from dependencies
    SELECT DISTINCT
        md.metric_id,
        md.metric_name,
        md.dependent_object_id as column_id,
        col.object_name as column_name,
        col.object_type,
        col.object_subtype,
        md.dependency_type
    FROM metric_dependencies md
    INNER JOIN dssobjects col ON md.dependent_object_id = col.object_id
    WHERE col.object_type = 12  -- Column object type (adjust if different)
)
SELECT
    co.metric_id,
    co.metric_name,
    co.column_id,
    co.column_name,
    co.object_type,
    co.object_subtype,
    co.dependency_type,
    -- Additional column metadata if available
    attr.attribute_name,
    attr.attribute_form_name,
    attr.data_type,
    attr.column_alias
FROM column_objects co
LEFT JOIN dssobjattr attr ON co.column_id = attr.object_id
ORDER BY co.metric_name, co.column_name;

-- Query 4: Alternative Query Using Expression Dependencies
-- This query looks for column references in metric expressions
WITH report_metrics AS (
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM dssobjects rep
    INNER JOIN dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssobjects met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3
        AND rep.object_subtype = 768
        AND met.object_type = 4
        AND met.object_subtype = 1024
)
SELECT DISTINCT
    rm.metric_id,
    rm.metric_name,
    expr.expression_id,
    expr.expression_text,
    col.object_id as column_id,
    col.object_name as column_name,
    col.object_type as column_object_type,
    col.object_subtype as column_object_subtype
FROM report_metrics rm
INNER JOIN dssexpression expr ON rm.metric_id = expr.object_id
INNER JOIN dssexpressiontokens token ON expr.expression_id = token.expression_id
INNER JOIN dssobjects col ON token.object_id = col.object_id
WHERE col.object_type IN (12, 13)  -- Column and attribute column types
ORDER BY rm.metric_name, col.object_name;

-- Query 5: Find Physical Table Columns Referenced by Metrics
-- This query identifies the actual database table columns used by metrics
WITH report_metrics AS (
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM dssobjects rep
    INNER JOIN dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssobjects met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3
        AND rep.object_subtype = 768
        AND met.object_type = 4
        AND met.object_subtype = 1024
)
SELECT DISTINCT
    rm.metric_id,
    rm.metric_name,
    pt.table_name,
    pc.column_name as physical_column_name,
    pc.data_type,
    pc.column_length,
    pc.column_precision,
    pc.column_scale,
    attr.object_id as attribute_id,
    attr.object_name as attribute_name
FROM report_metrics rm
INNER JOIN dssobjdep dep1 ON rm.metric_id = dep1.parent_object_id
INNER JOIN dssobjects attr ON dep1.child_object_id = attr.object_id
INNER JOIN dssobjdep dep2 ON attr.object_id = dep2.parent_object_id
INNER JOIN dssphysicalcolumn pc ON dep2.child_object_id = pc.column_id
INNER JOIN dssphysicaltable pt ON pc.table_id = pt.table_id
WHERE attr.object_type IN (12, 13)  -- Attribute/Column types
ORDER BY rm.metric_name, pt.table_name, pc.column_name;

-- Query 6: Comprehensive Report Analysis - All Dependencies
-- This query provides a complete view of all objects the report depends on
WITH RECURSIVE report_dependencies AS (
    -- Base case: Direct dependencies of the report
    SELECT
        rep.object_id as root_object_id,
        rep.object_name as root_object_name,
        dep.child_object_id as dependent_object_id,
        obj.object_name as dependent_object_name,
        obj.object_type,
        obj.object_subtype,
        1 as dependency_level,
        CAST(obj.object_name AS VARCHAR(1000)) as dependency_path
    FROM dssobjects rep
    INNER JOIN dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssobjects obj ON dep.child_object_id = obj.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3
        AND rep.object_subtype = 768

    UNION ALL

    -- Recursive case: Dependencies of dependencies
    SELECT
        rd.root_object_id,
        rd.root_object_name,
        dep.child_object_id,
        obj.object_name,
        obj.object_type,
        obj.object_subtype,
        rd.dependency_level + 1,
        CAST(rd.dependency_path + ' -> ' + obj.object_name AS VARCHAR(1000))
    FROM report_dependencies rd
    INNER JOIN dssobjdep dep ON rd.dependent_object_id = dep.parent_object_id
    INNER JOIN dssobjects obj ON dep.child_object_id = obj.object_id
    WHERE rd.dependency_level < 5  -- Prevent infinite recursion
)
SELECT DISTINCT
    rd.root_object_name as report_name,
    rd.dependent_object_id as object_id,
    rd.dependent_object_name as object_name,
    rd.object_type,
    rd.object_subtype,
    CASE
        WHEN rd.object_type = 4 AND rd.object_subtype = 1024 THEN 'Metric'
        WHEN rd.object_type = 12 THEN 'Column'
        WHEN rd.object_type = 13 THEN 'Attribute'
        WHEN rd.object_type = 1 THEN 'Attribute Form'
        WHEN rd.object_type = 3 THEN 'Report'
        WHEN rd.object_type = 8 THEN 'Folder'
        ELSE 'Other (' + CAST(rd.object_type AS VARCHAR) + ')'
    END as object_type_name,
    rd.dependency_level,
    rd.dependency_path
FROM report_dependencies rd
WHERE rd.object_type IN (4, 12, 13, 1)  -- Focus on metrics, columns, attributes
ORDER BY rd.object_type, rd.dependent_object_name;

-- Query 7: Summary - Column Objects Count by Metric
-- This query provides a summary count of column dependencies per metric
WITH report_metrics AS (
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM dssobjects rep
    INNER JOIN dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssobjects met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3
        AND rep.object_subtype = 768
        AND met.object_type = 4
        AND met.object_subtype = 1024
),
metric_column_deps AS (
    SELECT DISTINCT
        rm.metric_id,
        rm.metric_name,
        dep.child_object_id as column_id
    FROM report_metrics rm
    INNER JOIN dssobjdep dep ON rm.metric_id = dep.parent_object_id
    INNER JOIN dssobjects col ON dep.child_object_id = col.object_id
    WHERE col.object_type IN (12, 13)  -- Column/Attribute types
)
SELECT
    mcd.metric_name,
    COUNT(DISTINCT mcd.column_id) as column_count,
    STRING_AGG(col.object_name, ', ') as column_names
FROM metric_column_deps mcd
INNER JOIN dssobjects col ON mcd.column_id = col.object_id
GROUP BY mcd.metric_name
ORDER BY column_count DESC, mcd.metric_name;

-- Query 8: Detailed Column Information with Data Types
-- This query provides detailed information about each column dependency
SELECT DISTINCT
    met.object_name as metric_name,
    col.object_id as column_id,
    col.object_name as column_name,
    col.object_type,
    col.object_subtype,
    attr.data_type,
    attr.column_length,
    attr.column_precision,
    attr.column_scale,
    attr.is_nullable,
    pt.table_name as source_table,
    pc.column_name as physical_column_name
FROM dssobjects rep
INNER JOIN dssobjdep rep_dep ON rep.object_id = rep_dep.parent_object_id
INNER JOIN dssobjects met ON rep_dep.child_object_id = met.object_id
INNER JOIN dssobjdep met_dep ON met.object_id = met_dep.parent_object_id
INNER JOIN dssobjects col ON met_dep.child_object_id = col.object_id
LEFT JOIN dssobjattr attr ON col.object_id = attr.object_id
LEFT JOIN dssobjdep col_dep ON col.object_id = col_dep.parent_object_id
LEFT JOIN dssphysicalcolumn pc ON col_dep.child_object_id = pc.column_id
LEFT JOIN dssphysicaltable pt ON pc.table_id = pt.table_id
WHERE rep.object_name = 'CS0938327FFReport2'
    AND rep.object_type = 3
    AND rep.object_subtype = 768
    AND met.object_type = 4
    AND met.object_subtype = 1024
    AND col.object_type IN (12, 13)
ORDER BY met.object_name, col.object_name;

-- =====================================================================================
-- EXECUTION NOTES:
-- =====================================================================================
-- 1. Run Query 1 first to verify the report exists in the metadata
-- 2. Run Query 2 to see all metrics used in the report
-- 3. Run Query 3 for the main column dependency analysis
-- 4. Use Query 4 if expression-based analysis is needed
-- 5. Use Query 5 for physical table column mapping
-- 6. Use Query 6 for comprehensive dependency analysis
-- 7. Use Query 7 for summary statistics
-- 8. Use Query 8 for detailed column information
--
-- Note: Table names (dssobjects, dssobjdep, etc.) may vary depending on your
-- MicroStrategy metadata database schema version. Adjust table names as needed.
-- =====================================================================================