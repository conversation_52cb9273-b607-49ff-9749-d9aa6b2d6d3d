# =====================================================================================
# PowerShell Script: Get Managed Columns for MicroStrategy Report
# Usage: .\get_managed_columns.ps1 "ReportName"
# Example: .\get_managed_columns.ps1 "CS0938327FFReport2"
# =====================================================================================

param(
    [Parameter(Mandatory=$true)]
    [string]$ReportName
)

# Database connection parameters
$Host_DB = "************"
$Port = "5432"
$User = "mstr"
$Database = "poc_metadata"
$env:PGPASSWORD = "zJ7MhP8mdRCI"
$env:PGCLIENTENCODING = "UTF8"

# Function to write colored output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Error-Custom($Message) {
    Write-ColorOutput Red "ERROR: $Message"
}

function Write-Success($Message) {
    Write-ColorOutput Green "✓ $Message"
}

function Write-Info($Message) {
    Write-ColorOutput Cyan "$Message"
}

# Display header
Write-Host "====================================================================================="
Write-Info "MicroStrategy Managed Column Analysis"
Write-Host "====================================================================================="
Write-Host "Report Name: $ReportName"
Write-Host "Database: $Database on ${Host_DB}:$Port"
Write-Host "User: $User"
Write-Host ""

# Test database connection
Write-Info "Testing database connection..."
try {
    $null = psql -h $Host_DB -p $Port -U $User -d $Database -c "SELECT 1;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Database connection successful!"
    } else {
        throw "Connection failed"
    }
} catch {
    Write-Error-Custom "Cannot connect to database!"
    Write-Host "Please check:"
    Write-Host "- Database server is running"
    Write-Host "- Network connectivity"
    Write-Host "- Credentials are correct"
    exit 1
}
Write-Host ""

# Create the SQL query with the report name parameter
$SqlQuery = @"
SELECT DISTINCT 
    r.object_name as report_name,
    r.object_id as report_id,
    m.object_name as metric_name,
    m.object_id as metric_id,
    mc.object_name as managed_column_name,
    mc.object_id as managed_column_id
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d1 ON r.object_id = d1.object_id
INNER JOIN dssmdobjinfo m ON d1.depn_objid = m.object_id
INNER JOIN dssmdobjdepn d2 ON m.object_id = d2.object_id
INNER JOIN dssmdobjinfo mc ON d2.depn_objid = mc.object_id
WHERE r.object_name = '$ReportName'
    AND r.object_type = 3 AND r.subtype = 768
    AND m.object_type = 4 AND m.subtype = 1024
    AND mc.object_type = 26 AND mc.subtype = 6656 AND mc.extended_type = 3
ORDER BY m.object_name, mc.object_name;
"@

Write-Info "Executing query for report: $ReportName"
Write-Host "====================================================================================="

# Execute the query
try {
    $result = psql -h $Host_DB -p $Port -U $User -d $Database -c $SqlQuery 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Query execution failed: $result"
    }
    Write-Output $result
} catch {
    Write-Host ""
    Write-Error-Custom "Query execution failed!"
    Write-Host "Error details: $_"
    exit 1
}

Write-Host ""
Write-Host "====================================================================================="
Write-Success "Analysis completed successfully!"
Write-Host ""
Write-Info "Column descriptions:"
Write-Host "- report_name: Name of the MicroStrategy report"
Write-Host "- report_id: Unique identifier of the report"
Write-Host "- metric_name: Name of the metric within the report"
Write-Host "- metric_id: Unique identifier of the metric"
Write-Host "- managed_column_name: Name of the managed column used by the metric"
Write-Host "- managed_column_id: Unique identifier of the managed column"
Write-Host "====================================================================================="

# Ask if user wants to save results to file
Write-Host ""
$SaveToFile = Read-Host "Save results to file? (y/n)"
if ($SaveToFile -match "^[Yy]") {
    # Create filename with timestamp
    $Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $OutputFile = "managed_columns_${ReportName}_${Timestamp}.csv"
    # Clean filename
    $OutputFile = $OutputFile -replace '[^\w\-_\.]', '_'
    
    Write-Host ""
    Write-Info "Saving results to: $OutputFile"
    
    # Create CSV header
    "report_name,report_id,metric_name,metric_id,managed_column_name,managed_column_id" | Out-File -FilePath $OutputFile -Encoding UTF8
    
    # Execute query with CSV format
    $CsvQuery = "COPY ($SqlQuery) TO STDOUT WITH CSV;"
    psql -h $Host_DB -p $Port -U $User -d $Database -c $CsvQuery | Add-Content -Path $OutputFile -Encoding UTF8
    
    Write-Success "Results saved to: $OutputFile"
}

Write-Host ""
