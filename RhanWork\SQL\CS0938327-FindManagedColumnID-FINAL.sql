-- FINAL CORRECTED Query: Find Column Dependencies for Metrics in Report CS0938327FFReport2
-- Database: poc_metadata (PostgreSQL) - Server: 10.27.72.202:5432
-- Based on ACTUAL database structure analysis

-- =====================================================================================
-- DATABASE ANALYSIS SUMMARY:
-- =====================================================================================
-- Report: CS0938327FFReport2 (ID: 0E9F35104A20F9EB83AAC9B7B9C613C5)
-- Tables: dssmdobjinfo (objects), dssmdobjdepn (dependencies)
-- Key Columns: object_id, object_name, object_type, subtype, depn_objid
-- Object Types: Report(3), Metric(4), Attribute(12), Form(21)

-- =====================================================================================
-- QUERY 1: VERIFY REPORT EXISTS AND GET BASIC INFO
-- =====================================================================================

SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    create_time,
    mod_time
FROM dssmdobjinfo 
WHERE object_name = 'CS0938327FFReport2'
    AND object_type = 3
    AND subtype = 768;

-- =====================================================================================
-- QUERY 2: GET ALL METRICS USED IN THE REPORT
-- =====================================================================================

SELECT DISTINCT
    m.object_id as metric_id,
    m.object_name as metric_name,
    m.object_type,
    m.subtype
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
WHERE r.object_name = 'CS0938327FFReport2'
    AND r.object_type = 3
    AND r.subtype = 768
    AND m.object_type = 4
    AND m.subtype = 1024
ORDER BY m.object_name;

-- =====================================================================================
-- QUERY 3: GET ALL COLUMN OBJECTS DIRECTLY USED BY THE REPORT
-- =====================================================================================

SELECT DISTINCT
    c.object_id as column_id,
    c.object_name as column_name,
    c.object_type,
    c.subtype,
    CASE 
        WHEN c.object_type = 1 THEN 'Attribute Form'
        WHEN c.object_type = 4 THEN 'Metric'
        WHEN c.object_type = 12 THEN 'Attribute'
        WHEN c.object_type = 21 THEN 'Form'
        ELSE 'Other (' || c.object_type || ')'
    END as object_type_description
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
WHERE r.object_name = 'CS0938327FFReport2'
    AND r.object_type = 3
    AND r.subtype = 768
    AND c.object_type IN (1, 4, 12, 21)  -- Forms, Metrics, Attributes, Forms
ORDER BY c.object_type, c.object_name;

-- =====================================================================================
-- QUERY 4: GET COLUMN DEPENDENCIES FOR EACH METRIC IN THE REPORT
-- =====================================================================================

WITH report_metrics AS (
    -- Get metrics used in the report
    SELECT DISTINCT
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3
        AND r.subtype = 768
        AND m.object_type = 4
        AND m.subtype = 1024
),
metric_dependencies AS (
    -- Get what each metric depends on
    SELECT DISTINCT
        rm.metric_id,
        rm.metric_name,
        c.object_id as dependent_object_id,
        c.object_name as dependent_object_name,
        c.object_type,
        c.subtype
    FROM report_metrics rm
    INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
    INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
    WHERE c.object_type IN (1, 12, 13, 21)  -- Columns, Attributes, Forms
)
SELECT 
    md.metric_name,
    md.dependent_object_id as column_id,
    md.dependent_object_name as column_name,
    md.object_type,
    md.subtype,
    CASE 
        WHEN md.object_type = 1 THEN 'Attribute Form'
        WHEN md.object_type = 12 THEN 'Attribute'
        WHEN md.object_type = 13 THEN 'Attribute Element'
        WHEN md.object_type = 21 THEN 'Form'
        ELSE 'Other (' || md.object_type || ')'
    END as object_type_description
FROM metric_dependencies md
ORDER BY md.metric_name, md.object_type, md.dependent_object_name;

-- =====================================================================================
-- QUERY 5: COMPREHENSIVE ANALYSIS - ALL COLUMN OBJECTS RELATED TO THE REPORT
-- =====================================================================================

WITH RECURSIVE report_dependencies AS (
    -- Base case: Direct dependencies of the report
    SELECT 
        r.object_id as report_id,
        r.object_name as report_name,
        d.depn_objid as dependent_object_id,
        c.object_name as dependent_object_name,
        c.object_type,
        c.subtype,
        1 as dependency_level,
        'DIRECT' as dependency_source
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3
        AND r.subtype = 768
    
    UNION ALL
    
    -- Recursive case: Dependencies of metrics
    SELECT 
        rd.report_id,
        rd.report_name,
        d.depn_objid,
        c.object_name,
        c.object_type,
        c.subtype,
        rd.dependency_level + 1,
        'VIA_METRIC' as dependency_source
    FROM report_dependencies rd
    INNER JOIN dssmdobjdepn d ON rd.dependent_object_id = d.object_id
    INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
    WHERE rd.dependency_level = 1
        AND rd.object_type = 4  -- Only follow metric dependencies
        AND rd.dependency_level < 3  -- Limit recursion
)
SELECT DISTINCT
    rd.dependent_object_id as object_id,
    rd.dependent_object_name as object_name,
    rd.object_type,
    rd.subtype,
    CASE 
        WHEN rd.object_type = 1 THEN 'Attribute Form'
        WHEN rd.object_type = 4 THEN 'Metric'
        WHEN rd.object_type = 12 THEN 'Attribute'
        WHEN rd.object_type = 13 THEN 'Attribute Element'
        WHEN rd.object_type = 21 THEN 'Form'
        ELSE 'Other (' || rd.object_type || ')'
    END as object_type_description,
    rd.dependency_level,
    rd.dependency_source
FROM report_dependencies rd
WHERE rd.object_type IN (1, 4, 12, 13, 21)  -- Focus on relevant object types
ORDER BY rd.object_type, rd.dependency_level, rd.dependent_object_name;

-- =====================================================================================
-- QUERY 6: SUMMARY - COUNT OF DEPENDENCIES BY TYPE
-- =====================================================================================

SELECT 
    c.object_type,
    CASE 
        WHEN c.object_type = 1 THEN 'Attribute Form'
        WHEN c.object_type = 4 THEN 'Metric'
        WHEN c.object_type = 12 THEN 'Attribute'
        WHEN c.object_type = 13 THEN 'Attribute Element'
        WHEN c.object_type = 21 THEN 'Form'
        ELSE 'Other'
    END as object_type_description,
    COUNT(DISTINCT c.object_id) as object_count
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
WHERE r.object_name = 'CS0938327FFReport2'
    AND r.object_type = 3
    AND r.subtype = 768
GROUP BY c.object_type
ORDER BY c.object_type;

-- =====================================================================================
-- EXECUTION NOTES:
-- =====================================================================================
-- 1. Query 1: Confirms report exists (should return 1 row)
-- 2. Query 2: Shows metrics in the report (should return 2 metrics)
-- 3. Query 3: Shows all direct dependencies (attributes, forms, metrics)
-- 4. Query 4: Shows column dependencies specifically for metrics (may be empty if metrics don't have column deps)
-- 5. Query 5: Comprehensive recursive analysis of all dependencies
-- 6. Query 6: Summary count by object type
--
-- Based on analysis: Report has 2 metrics and 13 attributes/columns directly
-- =====================================================================================
