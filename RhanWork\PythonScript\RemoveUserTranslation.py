# mstrio-api		
from mstrio.connection import Connection		
		
from mstrio.object_management import (		
    Translation,		
    list_translations	
)
		
mstr_base_url = "http://rhanvm2019:8080/MicroStrategyLibrary/api"		
mstr_username = "administrator"		
mstr_password = ""		
mstr_project_name = "MicroStrategy Tutorial"		
bVerify = False		
		
def main():	
    print("--->Start")	
    try:
        CONN = Connection( base_url = mstr_base_url, 
                                username = mstr_username, 
                                password = mstr_password, 
                                project_name = mstr_project_name, 	
                                ssl_verify = bVerify 
                                )
        sUserId = "52DA953842F39084AC56A38D7E83B02A"
            
        userTran = list_translations( connection = CONN, id = sUserId, object_type = 34, project_id=CONN.project_id )[0]
        sTargetId = userTran.translation_target_id
        ### ltran = [Translation.OperationData( target_language = 1033,  target_id = sTargetId )]
        ltran = [Translation.OperationData( target_language = 1041,  target_id = sTargetId )]

        # Show project id
        print("Project id--->" + CONN.project_id)

        Translation.remove_translation(
            connection = CONN, id = sUserId, object_type = 34, translations = ltran,  project_id=CONN.project_id )
    
        CONN.close()

    except Exception as e:
        print("Unexpected error occurred")

    print("End<---")

# Call the main function
if __name__ == "__main__":
    main()