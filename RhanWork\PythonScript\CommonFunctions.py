"""
©2023 MicroStrategy. All rights reserved.
Application : 
Program     : 
Purpose     :
Modification History
When       : Who    : What
2024/2/1  : MSTR Ikeda : 新規作成
2024/4/1  : MSTR Ikeda : 変更（exportToSharedFolder追加）
"""
# パッケージ読み込み
import os
import sys
sys.path.append( os.path.join( os.path.dirname(__file__), "." ) )
import logging
import datetime
import pprint
import pandas as pd
import pandas.io.formats.excel
import openpyxl
import json
import pathlib
from typing import Tuple
import gc
import re
import shutil
import subprocess


# ログ設定
logger = logging.getLogger("log").getChild("sub")



def loadExcelData( sPath : str ) -> Tuple[ pd.DataFrame, pathlib.Path ]:

    logger.info( f"Input File is [{sPath}]." )

    
    try:

        path = pathlib.Path( sPath )

        df = pd.read_excel( str( path ) )

        return df, path

    
    except Exception as other:
            
        logger.error( f"{other}" )

        raise

def exportToExcel( df : pd.DataFrame, path : pathlib.Path, sFileName : str ):

    df.reset_index( inplace = True, drop = True )

    # Excel出力時に罫線をつけないように設定
    pd.io.formats.excel.ExcelFormatter.header_style = None


    sFile = f"{path.parent.parent}\\output\\{sFileName}.xlsx"
    logger.info( f"Export file is [{sFile}]." )

    # mode=a:上書きモード, w:新規作成
    with pd.ExcelWriter( sFile, mode = "w" ) as writer:
        df.to_excel( writer, sheet_name = "output", index = False  )

    #dataframeオブジェクトを削除し、メモリ開放
    del df
    gc.collect()

def exportToSharedFolder( sSharedFolderPath : str, sOSUser : str , sOSPwd : str, sInputFilePath : str):

    # ネットワーク共有フォルダにログイン
    subprocess.run( ['net', 'use', sSharedFolderPath, '/user:' + sOSUser, sOSPwd] )

    # ファイルのコピー(copy2は同名ファイルを上書き)
    shutil.copy2( sInputFilePath, sSharedFolderPath )

    # ネットワーク共有フォルダからログアウト
    subprocess.run( ['net', 'use',  sSharedFolderPath, '/delete'] )   



def removeString( txt ):
    
    txt = re.sub( "[\r\n\t]", "", txt)
    
    return txt