-- PostgreSQL version of the query
-- Converted from Teradata SQL

SELECT 
"T90"."IJ期初ABCD" AS "IJ期初ABCD＿当期"
,"T90"."IJ5月ABCD_12ヵ月移動累計" AS "IJ5月ABCD＿12ヵ月移動累計"
,"T90"."IJ6月ABCD_12ヵ月移動累計" AS "IJ6月ABCD＿12ヵ月移動累計"
,"T90"."IJ7月ABCD_12ヵ月移動累計" AS "IJ7月ABCD＿12ヵ月移動累計"
,"T90"."IJ8月ABCD_12ヵ月移動累計" AS "IJ8月ABCD＿12ヵ月移動累計"
,"T90"."IJ9月ABCD_12ヵ月移動累計" AS "IJ9月ABCD＿12ヵ月移動累計"
,"T90"."IJ10月ABCD_12ヵ月移動累計" AS "IJ10月ABCD＿12ヵ月移動累計"
,"T90"."IJ11月ABCD_12ヵ月移動累計" AS "IJ11月ABCD＿12ヵ月移動累計"
,"T90"."IJ12月ABCD_12ヵ月移動累計" AS "IJ12月ABCD＿12ヵ月移動累計"
,"T90"."IJ1月ABCD_12ヵ月移動累計" AS "IJ1月ABCD＿12ヵ月移動累計"
,"T90"."IJ2月ABCD_12ヵ月移動累計" AS "IJ2月ABCD＿12ヵ月移動累計"
,"T90"."IJ3月ABCD_12ヵ月移動累計" AS "IJ3月ABCD＿12ヵ月移動累計"
,"T90"."IJ期初金額_前期1年累計" AS "IJ期初金額＿前期1年累計"
,"T90"."IJ期初本数_前期1年累計" AS "IJ期初本数＿前期1年累計"
FROM 
"31AS_直販IJ孔版ABCD顧客分類" "T90"
LIMIT 100;
