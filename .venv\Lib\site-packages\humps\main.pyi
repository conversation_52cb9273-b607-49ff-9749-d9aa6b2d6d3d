from typing import Iterable, TypeVar, Mapping, Union


StrOrIter = TypeVar('StrOrIter', bound=Union[str, Mapping, list])


def pascalize(str_or_iter: StrOrIter) -> StrOrIter: ...
def camelize(str_or_iter: StrOrIter) -> StrOrIter: ...
def kebabize(str_or_iter: StrOrIter) -> StrOrIter: ...
def decamelize(str_or_iter: StrOrIter) -> StrOrIter: ...
def depascalize(str_or_iter: StrOrIter) -> StrOrIter: ...
def dekebabize(str_or_iter: StrOrIter) -> StrOrIter: ...
def is_camelcase(str_or_iter: StrOrIter) -> bool: ...
def is_pascalcase(str_or_iter: StrOrIter) -> bool: ...
def is_kebabcase(str_or_iter: StrOrIter) -> bool: ...
def is_snakecase(str_or_iter: StrOrIter) -> bool: ...
