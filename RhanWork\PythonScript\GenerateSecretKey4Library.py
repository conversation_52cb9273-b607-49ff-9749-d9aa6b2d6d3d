import os
import base64
import platform
def generate_token():
    # Generate 64 bytes of random data (512 bits)
    random_bytes = os.urandom(64)
    # Encode the random bytes using base64
    encoded_token = base64.b64encode(random_bytes)
    # Convert bytes to string
    token = encoded_token.decode('utf-8')
    return token
def main():
    token = generate_token()
    print("Generated Token:", token)
if __name__ == "__main__":
    main()