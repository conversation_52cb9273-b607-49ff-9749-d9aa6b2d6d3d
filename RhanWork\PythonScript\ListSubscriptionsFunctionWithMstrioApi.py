# mstrio-api		
from mstrio.connection import Connection
from mstrio.connection import get_connection
from mstrio.api import subscriptions as Sub
import json
import traceback

from mstrio.object_management import (		
    Translation,		
    list_translations	
)
		
mstr_base_url = "http://rhanvm2019:8080/MicroStrategyLibrary/api"		
mstr_username = "administrator"		
mstr_password = ""		
mstr_project_name = "MicroStrategy Tutorial"		
bVerify = False		
		
def main():	
    print("--->Start")	
    try:
        CONN = Connection( base_url = mstr_base_url, 
                                username = mstr_username, 
                                password = mstr_password, 
                                project_name = mstr_project_name, 	
                                ssl_verify = bVerify 
                                )
        '''
        sUserId = "52DA953842F39084AC56A38D7E83B02A"
            
        userTran = list_translations( connection = CONN, id = sUserId, object_type = 34, project_id=CONN.project_id )[0]
        sTargetId = userTran.translation_target_id
        ### ltran = [Translation.OperationData( target_language = 1033,  target_id = sTargetId )]
        ltran = [Translation.OperationData( target_language = 1041,  target_id = sTargetId )]

        # Show project id
        print("Project id--->" + CONN.project_id)

        Translation.remove_translation(
            connection = CONN, id = sUserId, object_type = 34, translations = ltran,  project_id=CONN.project_id )
        '''

        # Get list of subscriptions
        # subList = list_subscriptions(connection=CONN, limit=10000)
        # print("Size of subscription list--->" + str(len(subList)))

        ### conn = get_connection(workstation_data = workstationData)

        subscriptionList = Sub.list_subscriptions(CONN, project_id = 'B7CA92F04B9FAE8D941C3E9B7E0CD754') ###, limit = -1)
        ### print(subscriptionList._content.len())
        ### print(len(subscriptionList._content['subscriptions']))

        data = json.loads(subscriptionList._content)
        
        ### print(len(data))
        print(len(data['subscriptions'])) ###[0])
        ###print(subscriptionList._content)

        CONN.close()

    except Exception as e:
        t = traceback.format_exception(type(e), e, e.__traceback__)
        print(t)

    print("End<---")

# Call the main function
if __name__ == "__main__":
    main()