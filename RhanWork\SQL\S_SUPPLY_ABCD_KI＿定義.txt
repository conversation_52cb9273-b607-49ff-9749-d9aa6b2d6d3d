CREATE SET TABLE risodb.S_SUPPLY_ABCD_KI ,<PERSON><PERSON><PERSON><PERSON><PERSON> ,
     NO BEFORE JOURNAL,
     NO AFTER JOURNAL,
     CHECKSUM = DEFAULT,
     DEFAULT MERGEBLOCKRATIO,
     MAP = TD_MAP1
     (
      S_CO<PERSON>_<PERSON><PERSON><PERSON>A<PERSON><PERSON><PERSON>IG<PERSON>OSYO VARCHAR(10) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_CODE_KOKYAKU VARCHAR(7) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_<PERSON><PERSON><PERSON>_<PERSON><PERSON>YAKUMEI VARCHAR(120) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_<PERSON>DE_YUBIN VARCHAR(8) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_<PERSON><PERSON>J_JYUSHO VARCHAR(248) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_DATE_URIAGE_KI INTEGER,
      S_<PERSON>ATE_NEN INTEGER,
      S_KB<PERSON>_NEWJG VARCHAR(10) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD04 VARCHAR(2) <PERSON><PERSON><PERSON><PERSON><PERSON> SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD05 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD06 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD07 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD08 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD09 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD10 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD11 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD12 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD01 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD02 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_ABCD03 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_KINGAKU DECIMAL(13,0),
      S_NUM_IJ_SURYO DECIMAL(9,0),
      S_NUM_IJ_SURYO_1000 DECIMAL(9,0),
      S_NUM_RG_ABCD04 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD05 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD06 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD07 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD08 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD09 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD10 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD11 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD12 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD01 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD02 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_ABCD03 VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_KINGAKU DECIMAL(13,0),
      S_NUM_IJ_ABCD_NOW VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_IJ_KINGAKU_NOW DECIMAL(13,0),
      S_NUM_IJ_SURYO_NOW DECIMAL(9,0),
      S_NUM_IJ_SURYO_1000_NOW DECIMAL(9,0),
      S_NUM_RG_ABCD_NOW VARCHAR(2) CHARACTER SET UNICODE NOT CASESPECIFIC,
      S_NUM_RG_KINGAKU_NOW DECIMAL(13,0))
UNIQUE PRIMARY INDEX ( S_CODE_KOKYAKUJIGYOSYO ,S_DATE_URIAGE_KI );
