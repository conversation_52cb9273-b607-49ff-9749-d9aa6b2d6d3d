-- Diagnostic Queries to Identify MicroStrategy Metadata Tables
-- Database: poc_metadata (PostgreSQL)
-- Purpose: Find the correct table names and structure

-- =====================================================================================
-- STEP 1: IDENTIFY AVAILABLE TABLES
-- =====================================================================================

-- Query 1: Find all tables that might contain MicroStrategy metadata
SELECT 
    table_schema,
    table_name,
    table_type
FROM information_schema.tables 
WHERE (
    table_name ILIKE '%object%' 
    OR table_name ILIKE '%dss%'
    OR table_name ILIKE '%mstr%'
    OR table_name ILIKE '%metadata%'
    OR table_name ILIKE '%report%'
    OR table_name ILIKE '%metric%'
    OR table_name ILIKE '%dependency%'
    OR table_name ILIKE '%dep%'
)
AND table_schema = 'public'
ORDER BY table_name;

-- Query 2: List all tables in the public schema
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;

-- Query 3: Search for tables with specific patterns
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public'
    AND (
        table_name ~ '^[Dd][Ss][Ss].*' 
        OR table_name ~ '.*[Oo][Bb][Jj].*'
        OR table_name ~ '.*[Mm][Ee][Tt][Aa].*'
    )
ORDER BY table_name;

-- =====================================================================================
-- STEP 2: EXAMINE TABLE STRUCTURES (Run after identifying table names)
-- =====================================================================================

-- Query 4: Check columns in potential object tables
-- Replace 'TABLE_NAME' with the actual table name you found
/*
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'TABLE_NAME'  -- Replace with actual table name
ORDER BY ordinal_position;
*/

-- =====================================================================================
-- STEP 3: SAMPLE DATA QUERIES (Run after identifying correct tables)
-- =====================================================================================

-- Query 5: Sample data from objects table (replace table name)
/*
SELECT *
FROM TABLE_NAME  -- Replace with actual table name
LIMIT 10;
*/

-- Query 6: Look for report objects specifically
/*
SELECT *
FROM TABLE_NAME  -- Replace with actual table name
WHERE object_name ILIKE '%report%'
   OR object_type = 3
LIMIT 10;
*/

-- Query 7: Look for metric objects specifically
/*
SELECT *
FROM TABLE_NAME  -- Replace with actual table name
WHERE object_name ILIKE '%metric%'
   OR object_type = 4
LIMIT 10;
*/

-- =====================================================================================
-- STEP 4: COMMON MICROSTRATEGY TABLE NAME VARIATIONS TO TRY
-- =====================================================================================

-- Try these table name variations (uncomment one at a time):

-- Variation 1: Uppercase
-- SELECT * FROM DSSOBJECTS LIMIT 5;
-- SELECT * FROM DSSOBJDEP LIMIT 5;

-- Variation 2: Mixed case
-- SELECT * FROM DssObjects LIMIT 5;
-- SELECT * FROM DssObjDep LIMIT 5;

-- Variation 3: With schema prefix
-- SELECT * FROM public.dssobjects LIMIT 5;
-- SELECT * FROM metadata.dssobjects LIMIT 5;

-- Variation 4: Different naming convention
-- SELECT * FROM mstr_objects LIMIT 5;
-- SELECT * FROM mstr_object_dependencies LIMIT 5;
-- SELECT * FROM microstrategy_objects LIMIT 5;

-- Variation 5: Abbreviated names
-- SELECT * FROM obj LIMIT 5;
-- SELECT * FROM objects LIMIT 5;
-- SELECT * FROM obj_dep LIMIT 5;

-- =====================================================================================
-- STEP 5: SEARCH FOR SPECIFIC REPORT
-- =====================================================================================

-- Once you find the correct table name, use this pattern to search for your report:
/*
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype
FROM [CORRECT_TABLE_NAME]  -- Replace with actual table name
WHERE object_name = 'CS0938327FFReport2'
   OR object_name ILIKE '%CS0938327%'
   OR object_name ILIKE '%FFReport%';
*/

-- =====================================================================================
-- INSTRUCTIONS:
-- =====================================================================================
-- 1. Run Query 1 first to see all potential MicroStrategy tables
-- 2. Run Query 2 to see all tables in the public schema
-- 3. Identify the correct table names from the results
-- 4. Use Query 4 to examine the structure of the identified tables
-- 5. Use Queries 5-7 to sample data and confirm the table contents
-- 6. Try the variations in Step 4 if standard names don't work
-- 7. Use Step 5 to search for your specific report once tables are identified
-- =====================================================================================
