@echo off
chcp 65001 >nul
setlocal

if "%~1"=="" (
    echo ERROR: Report name required!
    echo Usage: %~nx0 "ReportName"
    echo Example: %~nx0 "FFSQLRptCreatedFromWorkStation"
    pause
    exit /b 1
)

set "REPORT_NAME=%~1"
set "HOST=************"
set "PORT=5432"
set "USER=mstr"
set "DATABASE=poc_metadata"
set "PGPASSWORD=zJ7MhP8mdRCI"
set "PGCLIENTENCODING=UTF8"

echo =====================================================================================
echo MicroStrategy Managed Column Analysis
echo =====================================================================================
echo Report Name: %REPORT_NAME%
echo Database: %DATABASE% on %HOST%:%PORT%
echo.

echo Testing connection...
psql -h %HOST% -p %PORT% -U %USER% -d %DATABASE% -c "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Cannot connect to database!
    pause
    exit /b 1
)
echo Connection successful!
echo.

echo Executing analysis...
echo =====================================================================================

psql -h %HOST% -p %PORT% -U %USER% -d %DATABASE% -c "SELECT DISTINCT r.object_name as report_name, r.object_id as report_id, m.object_name as metric_name, m.object_id as metric_id, mc.object_name as managed_column_name, mc.object_id as managed_column_id FROM dssmdobjinfo r INNER JOIN dssmdobjdepn d1 ON r.object_id = d1.object_id INNER JOIN dssmdobjinfo m ON d1.depn_objid = m.object_id INNER JOIN dssmdobjdepn d2 ON m.object_id = d2.object_id INNER JOIN dssmdobjinfo mc ON d2.depn_objid = mc.object_id WHERE r.object_name = '%REPORT_NAME%' AND r.object_type = 3 AND r.subtype = 768 AND m.object_type = 4 AND m.subtype = 1024 AND mc.object_type = 26 AND mc.subtype = 6656 AND mc.extended_type = 3 ORDER BY m.object_name, mc.object_name;"

echo.
echo =====================================================================================
echo Analysis completed!
echo =====================================================================================
pause
