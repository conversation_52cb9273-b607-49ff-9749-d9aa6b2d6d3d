# MicroStrategy Managed Column Analysis Tools

This directory contains batch programs to analyze MicroStrategy reports and extract managed column information.

## 📁 Files Overview

### SQL Files
- **`CS0938327-FindManagedColumnID.sql`** - Complete SQL solution with examples
- **`ManagedColumnResults.sql`** - Comprehensive analysis results
- **`ManagedColumnAnalysis.sql`** - Detailed analysis queries

### Batch Programs
- **`get_managed_columns.sh`** - Linux/Mac shell script ✅ **TESTED & WORKING**
- **`get_managed_columns.bat`** - Windows batch script
- **`get_managed_columns.ps1`** - PowerShell script

## 🚀 Quick Start

### Linux/Mac/WSL (Recommended)
```bash
# Make executable (first time only)
chmod +x get_managed_columns.sh

# Run analysis
./get_managed_columns.sh "CS0938327FFReport2"
./get_managed_columns.sh "YourReportName"
```

### Windows Command Prompt
```cmd
get_managed_columns.bat "CS0938327FFReport2"
get_managed_columns.bat "YourReportName"
```

### PowerShell
```powershell
.\get_managed_columns.ps1 "CS0938327FFReport2"
.\get_managed_columns.ps1 "YourReportName"
```

## 📊 Output Format

The programs output results in this format:
```
report_name | report_id | metric_name | metric_id | managed_column_name | managed_column_id
```

### Example Output
```
    report_name     |            report_id             |       metric_name       |            metric_id             |   managed_column_name   |        managed_column_id
--------------------+----------------------------------+-------------------------+----------------------------------+-------------------------+----------------------------------  
 CS0938327FFReport2 | 0E9F35104A20F9EB83AAC9B7B9C613C5 | IJ期初金額＿前期1年累計 | 3FCDB9CA41EB46AAAFD692528B54AA72 | IJ期初金額＿前期1年累計 | 5079544DF950476FBD5833798EDCE295   
 CS0938327FFReport2 | 0E9F35104A20F9EB83AAC9B7B9C613C5 | IJ期初本数＿前期1年累計 | B2627BE04448440B9AA772CE475D2C2A | IJ期初本数＿前期1年累計 | 6E25F8CA26BC48D0ADBDD366468BE365   
```

## ⚙️ Configuration

### Database Connection
All scripts are pre-configured with:
- **Host:** ************
- **Port:** 5432
- **Database:** poc_metadata
- **User:** mstr
- **Password:** zJ7MhP8mdRCI (embedded in scripts)

### Managed Column Criteria
The scripts search for managed columns with:
- **object_type:** 26
- **subtype:** 6656 (0x1A00)
- **extended_type:** 3

## 🔧 Features

### ✅ What Works
- **Parameter Input:** Pass report name as command line argument
- **Connection Testing:** Automatic database connection verification
- **Error Handling:** Clear error messages and troubleshooting tips
- **Colored Output:** Easy-to-read results with color coding (shell script)
- **Result Export:** Option to save results to CSV file
- **Cross-Platform:** Multiple script versions for different environments

### 📋 Usage Examples

```bash
# Basic usage
./get_managed_columns.sh "CS0938327FFReport2"

# Different report
./get_managed_columns.sh "SalesReport2024"

# Report with spaces in name
./get_managed_columns.sh "Monthly Sales Analysis Report"
```

## 🛠️ Troubleshooting

### Common Issues

1. **Permission Denied (Linux/Mac)**
   ```bash
   chmod +x get_managed_columns.sh
   ```

2. **Database Connection Failed**
   - Check if PostgreSQL server is running
   - Verify network connectivity to ************:5432
   - Confirm credentials are correct

3. **No Results Found**
   - Verify report name is exact (case-sensitive)
   - Check if report contains metrics
   - Ensure metrics use managed columns

4. **Encoding Issues**
   - Scripts handle UTF-8 encoding for Japanese characters
   - CSV export may have minor encoding issues but main output works perfectly

## 📝 Column Descriptions

- **report_name:** Name of the MicroStrategy report
- **report_id:** Unique identifier of the report (GUID)
- **metric_name:** Name of the metric within the report
- **metric_id:** Unique identifier of the metric (GUID)
- **managed_column_name:** Name of the managed column used by the metric
- **managed_column_id:** Unique identifier of the managed column (GUID)

## 🎯 Success Criteria

✅ **VERIFIED WORKING:**
- Shell script tested successfully with CS0938327FFReport2
- Returns correct managed column information
- Proper error handling and user feedback
- Clean, formatted output matching requirements

The batch programs successfully provide the exact output format requested:
`report_name | report_id | metric_name | metric_id | managed_column_name | managed_column_id`
