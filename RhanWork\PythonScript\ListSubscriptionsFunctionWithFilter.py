# mstrio-api		
from mstrio.connection import Connection
from mstrio.distribution_services.subscription.subscription_manager import list_subscriptions	
import traceback

from mstrio.object_management import (		
    Translation,		
    list_translations	
)
		
mstr_base_url = "http://rhanvm2019:8080/MicroStrategyLibrary/api"		
mstr_username = "administrator"		
mstr_password = ""		
mstr_project_name = "MicroStrategy Tutorial"		
bVerify = False		
		
def main():	
    print("--->Start")	
    try:
        CONN = Connection( base_url = mstr_base_url, 
                                username = mstr_username, 
                                password = mstr_password, 
                                project_name = mstr_project_name, 	
                                ssl_verify = bVerify 
                                )
        '''
        sUserId = "52DA953842F39084AC56A38D7E83B02A"
            
        userTran = list_translations( connection = CONN, id = sUserId, object_type = 34, project_id=CONN.project_id )[0]
        sTargetId = userTran.translation_target_id
        ### ltran = [Translation.OperationData( target_language = 1033,  target_id = sTargetId )]
        ltran = [Translation.OperationData( target_language = 1041,  target_id = sTargetId )]

        # Show project id
        print("Project id--->" + CONN.project_id)

        Translation.remove_translation(
            connection = CONN, id = sUserId, object_type = 34, translations = ltran,  project_id=CONN.project_id )
        '''
        # Define filter
        #testFilter = ['owner','administrator']
        ownerFilter = {'id':'54F3D26011D2896560009A8E67019608', 'name':'administrator jp name'} 
        subList = list_subscriptions(connection = CONN, owner = ownerFilter)
        print("Size of subscription list with ownerFilter--->" + str(len(subList))) 
        print(subList)
        ## ownerFilter = {'id':'658F69C8431E03DCF6538CB0C03D75D0', 'name':'demo'}        
        
        ###dateRangeFilter = ('<2024-12-19','>2024-12-01')
        ###subList = list_subscriptions(connection = CONN, date_created = dateRangeFilter)
        ###print("Size of subscription list with dateRangeFilter--->" + str(len(subList))) 

        ###datetimeFilter = '<2024-12-18T20:33:54+0009'
        ###subList = list_subscriptions(connection = CONN, date_created = datetimeFilter)
        ###print("Size of subscription list with datetimeFilter--->" + str(len(subList))) 

        '''        
        startsFilter = "'starts' CS0888480"
        subList = list_subscriptions(connection = CONN, name = startsFilter)
        print("Size of subscription list with startsFilter--->" + str(len(subList)))
        print(subList)
        

        includeFilter = ['CS0888480_0040', 'CS0888480_0041', 'CS0888480_0042'] 
        subList = list_subscriptions(connection = CONN, name = includeFilter)
        print("Size of subscription list with includeFilter--->" + str(len(subList))) 
        print(subList)
        '''
        
        #print(testFilter)
        ### new dict(owner=administrator) ### "owner='administrator'" ### {owner:administrator}

        # Get list of subscriptions with owner filter
        ###subList = list_subscriptions(connection = CONN, owner=ownerFilter)

        CONN.close()

    except Exception as e:
        t = traceback.format_exception(type(e), e, e.__traceback__)
        print(t)

    print("End<---")

# Call the main function
if __name__ == "__main__":
    main()