-- CORRECTED Query to Find Column Dependencies for Metrics in Report: CS0938327FFReport2
-- Database: poc_metadata (PostgreSQL)
-- Using correct MicroStrategy metadata table names

-- =====================================================================================
-- STEP 1: VERIFY REPORT EXISTS
-- =====================================================================================

-- First, let's check if the report exists and get its details
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype,
    date_created,
    date_modified
FROM dssmdobjinfo 
WHERE object_name = 'CS0938327FFReport2'
    AND object_type = 3      -- Report object type
    AND object_subtype = 768; -- Report subtype

-- =====================================================================================
-- STEP 2: FIND ALL METRICS IN THE REPORT
-- =====================================================================================

-- Find all metric objects used in the specified report
WITH report_metrics AS (
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name,
        met.object_type,
        met.object_subtype
    FROM dssmdobjinfo rep
    INNER JOIN dssmdobjdepn dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssmdobjinfo met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3      -- Report
        AND rep.object_subtype = 768 -- Report subtype
        AND met.object_type = 4      -- Metric
        AND met.object_subtype = 1024 -- Metric subtype
)
SELECT 
    metric_id,
    metric_name,
    object_type,
    object_subtype
FROM report_metrics
ORDER BY metric_name;

-- =====================================================================================
-- STEP 3: FIND COLUMN DEPENDENCIES FOR EACH METRIC
-- =====================================================================================

-- Main query: Get all column objects that metrics depend on
WITH report_metrics AS (
    -- Get all metrics used in the specified report
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM dssmdobjinfo rep
    INNER JOIN dssmdobjdepn dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssmdobjinfo met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3      -- Report object type
        AND rep.object_subtype = 768 -- Report subtype
        AND met.object_type = 4      -- Metric object type
        AND met.object_subtype = 1024 -- Metric subtype
),
column_dependencies AS (
    -- Get all column dependencies for each metric
    SELECT DISTINCT
        rm.metric_id,
        rm.metric_name,
        dep.child_object_id as column_id,
        col.object_name as column_name,
        col.object_type,
        col.object_subtype
    FROM report_metrics rm
    INNER JOIN dssmdobjdepn dep ON rm.metric_id = dep.parent_object_id
    INNER JOIN dssmdobjinfo col ON dep.child_object_id = col.object_id
    WHERE col.object_type IN (12, 13, 1) -- Column, Attribute, and Form object types
)
SELECT 
    cd.metric_name,
    cd.column_id,
    cd.column_name,
    cd.object_type,
    cd.object_subtype,
    CASE 
        WHEN cd.object_type = 1 THEN 'Attribute Form'
        WHEN cd.object_type = 12 THEN 'Column'
        WHEN cd.object_type = 13 THEN 'Attribute'
        ELSE 'Other (' || cd.object_type || ')'
    END as object_type_description
FROM column_dependencies cd
ORDER BY cd.metric_name, cd.column_name;

-- =====================================================================================
-- STEP 4: ALTERNATIVE QUERY - RECURSIVE DEPENDENCIES
-- =====================================================================================

-- This query finds dependencies recursively (dependencies of dependencies)
WITH RECURSIVE report_dependencies AS (
    -- Base case: Direct dependencies of metrics in the report
    SELECT 
        rep.object_id as report_id,
        rep.object_name as report_name,
        met.object_id as metric_id,
        met.object_name as metric_name,
        dep.child_object_id as dependent_object_id,
        obj.object_name as dependent_object_name,
        obj.object_type,
        obj.object_subtype,
        1 as dependency_level
    FROM dssmdobjinfo rep
    INNER JOIN dssmdobjdepn rep_dep ON rep.object_id = rep_dep.parent_object_id
    INNER JOIN dssmdobjinfo met ON rep_dep.child_object_id = met.object_id
    INNER JOIN dssmdobjdepn dep ON met.object_id = dep.parent_object_id
    INNER JOIN dssmdobjinfo obj ON dep.child_object_id = obj.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3
        AND rep.object_subtype = 768
        AND met.object_type = 4
        AND met.object_subtype = 1024
    
    UNION ALL
    
    -- Recursive case: Dependencies of dependencies
    SELECT 
        rd.report_id,
        rd.report_name,
        rd.metric_id,
        rd.metric_name,
        dep.child_object_id,
        obj.object_name,
        obj.object_type,
        obj.object_subtype,
        rd.dependency_level + 1
    FROM report_dependencies rd
    INNER JOIN dssmdobjdepn dep ON rd.dependent_object_id = dep.parent_object_id
    INNER JOIN dssmdobjinfo obj ON dep.child_object_id = obj.object_id
    WHERE rd.dependency_level < 3  -- Limit recursion depth
)
SELECT DISTINCT
    rd.metric_name,
    rd.dependent_object_id as object_id,
    rd.dependent_object_name as object_name,
    rd.object_type,
    rd.object_subtype,
    CASE 
        WHEN rd.object_type = 1 THEN 'Attribute Form'
        WHEN rd.object_type = 12 THEN 'Column'
        WHEN rd.object_type = 13 THEN 'Attribute'
        WHEN rd.object_type = 4 THEN 'Metric'
        ELSE 'Other (' || rd.object_type || ')'
    END as object_type_description,
    rd.dependency_level
FROM report_dependencies rd
WHERE rd.object_type IN (1, 12, 13)  -- Focus on columns, attributes, and forms
ORDER BY rd.metric_name, rd.dependency_level, rd.object_name;

-- =====================================================================================
-- STEP 5: SUMMARY QUERY
-- =====================================================================================

-- Summary: Count of column dependencies per metric
WITH report_metrics AS (
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM dssmdobjinfo rep
    INNER JOIN dssmdobjdepn dep ON rep.object_id = dep.parent_object_id
    INNER JOIN dssmdobjinfo met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3
        AND rep.object_subtype = 768
        AND met.object_type = 4
        AND met.object_subtype = 1024
),
metric_column_deps AS (
    SELECT DISTINCT
        rm.metric_id,
        rm.metric_name,
        dep.child_object_id as column_id
    FROM report_metrics rm
    INNER JOIN dssmdobjdepn dep ON rm.metric_id = dep.parent_object_id
    INNER JOIN dssmdobjinfo col ON dep.child_object_id = col.object_id
    WHERE col.object_type IN (1, 12, 13)
)
SELECT 
    mcd.metric_name,
    COUNT(DISTINCT mcd.column_id) as column_count,
    STRING_AGG(col.object_name, ', ' ORDER BY col.object_name) as column_names
FROM metric_column_deps mcd
INNER JOIN dssmdobjinfo col ON mcd.column_id = col.object_id
GROUP BY mcd.metric_name
ORDER BY column_count DESC, mcd.metric_name;

-- =====================================================================================
-- EXECUTION INSTRUCTIONS:
-- =====================================================================================
-- 1. Run Step 1 first to verify the report exists
-- 2. Run Step 2 to see all metrics in the report
-- 3. Run Step 3 for the main column dependency analysis
-- 4. Run Step 4 for recursive dependency analysis (optional)
-- 5. Run Step 5 for summary statistics
-- =====================================================================================
