2024-11-11 16:58:18,850 - main - INFO - Number of rows of input data is 1.
2024-11-11 16:58:21,264 - main - INFO - Status is True.
2024-11-11 16:58:21,265 - main - INFO - Target object id = [E47D7CA64FE6A207A2A814A8F1124656], Node Key = [W68], output file = [test_small.csv]
2024-11-11 16:58:21,265 - main - INFO - Get Document Instance Id.
2024-11-11 16:58:22,264 - main - DEBUG - Document Instance id is 792EC05D4A46F7B42AC124B7F3076FE6.
2024-11-11 16:58:22,265 - main - INFO - Export visualization data to csv.
2024-11-11 16:58:24,057 - main - DEBUG - 200
2024-11-11 16:58:24,058 - main - ERROR - Exception raised for row[0].
Traceback (most recent call last):
  File "C:\00work\20MSTR\00Case\_PythonScript\GetDocumentVizData_v02.py", line 199, in main
    with open( f"{path.parent.parent}\\output\\{sFileName}", 'wb' ) as file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'C:\\00work\\20MSTR\\00Case\\output\\test_small.csv'
2024-11-11 16:58:24,060 - main - INFO - Number of successful transactions: 0
2024-11-11 16:58:24,060 - main - INFO - Number of failed transactions: 1
