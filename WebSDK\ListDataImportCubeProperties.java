/////////////////////////////////////////////////////////////////////////////////////////////
// ©2023 MicroStrategy. All rights reserved.
// Application : MSTR Project Documentation
// Program     : ListDataImportCubeProperties
// Purpose     :
// Modification History
// When	: Who	: What
// 03/30/2023 	: T.Ikeda	: Created
// 04/21/2023 	: T.Ikeda	: Modified
/////////////////////////////////////////////////////////////////////////////////////////////

package com.microstrategy.sdk.tools;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Properties;

import com.microstrategy.web.objects.SimpleList;
import com.microstrategy.web.objects.WebFolder;
import com.microstrategy.web.objects.WebIServerSession;
import com.microstrategy.web.objects.WebObjectInfo;
import com.microstrategy.web.objects.WebObjectSource;
import com.microstrategy.web.objects.WebObjectsException;
import com.microstrategy.web.objects.WebObjectsFactory;
import com.microstrategy.web.objects.WebPropertyGroup;
import com.microstrategy.web.objects.WebPropertySet;
import com.microstrategy.web.objects.WebReportInstance;
import com.microstrategy.web.objects.WebReportSource;
import com.microstrategy.webapi.EnumDSSXMLApplicationType;
import com.microstrategy.webapi.EnumDSSXMLAuthModes;
import com.microstrategy.webapi.EnumDSSXMLExecutionFlags;
import com.microstrategy.webapi.EnumDSSXMLObjectFlags;
import com.microstrategy.webapi.EnumDSSXMLObjectSubTypes;
import com.microstrategy.webapi.EnumDSSXMLObjectTypes;

class ListDataImportCubeProperties
{
	private static final String sMsgFile = "message.properties";

	private static SimpleDateFormat sdf = new SimpleDateFormat( "yyyyMMdd" );

	private static final String sOutputFilePrefix = "DataImportCubeProperties_" + sdf.format( new Date() );

	private static final String sOutputFileSuffix = ".txt";

	private static final String sColumnSeparator = "\t";

	private static final String sPathSeparator = "\\";

	private static final String sColumnDelimiter = "";

	private static final String sElSep = ", ";

	private static String sOutputFileName = "";

	private static String sPath = "";

	private static boolean _m_debugMode = false;

	private static WebObjectsFactory _m_objectFactory = null;

	private static WebObjectSource _m_objectSource = null;

	private static WebIServerSession _m_serverSession = null;

	private static Properties _m_messageProperties = new Properties();

	private static StringBuffer sRecord = new StringBuffer();

	private static BufferedWriter _m_bw = null;

	private static int _m_objectCount = 0;

	private static final String sHiddenLabel = "Yes";

	private static ListDataImportCubeProperties.NodeInfo nodeInfo;

	static class NodeInfo
	{
		String id;

		String name;

		String absolutePath;

		String parentPath;

		boolean isHidden;

		String hiddenLabel;

		String modificationTime;

		String type;

		//WorkingySet
		//TreeMap<String, String> workingSet = new TreeMap<String, String>();

		String datasource1;

		String datasource2;

		String datasource3;


	}


	public static void main( String[] args )
	{

		//if( ! f_a() ) System.exit( 0 );

		//System.out.println( "This program permits temporary use." );

		try
		{
			if ( args.length <7 || args.length >8 )
			{
				System.out.println( "Error : You must specify valid arguments." );

				System.out.println( "USAGE: java ListDataImportCubeProperties <servername> <port> <authentication> <pjname> <username> <password> <objectID> <objectType>(option)" );

				System.exit( -1 );
			}

			openSession( args[0], Integer.parseInt( args[1] ), Integer.parseInt(args[2]), args[3], args[4], args[5] );

			sOutputFileName = sOutputFilePrefix + "_" + args[3] + sOutputFileSuffix;

			openPropertyFile();

			openOutputStream();

			if ( args.length == 7 )
			{
				getReportProperties( args[6] );

			}
			else
			{
				searchReportObjects( args[6] );
			}

			System.out.println( "[I][main] " + _m_objectCount + " nodes are exported." );
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			closeOutputStream();

			closeSession( _m_serverSession );
		}
	}

	private static void searchReportObjects( String sParentFolderId )
	{
		try
		{
			int objTypes[] = { EnumDSSXMLObjectTypes.DssXmlTypeFolder, EnumDSSXMLObjectTypes.DssXmlTypeReportDefinition };

			_m_objectSource = _m_objectFactory.getObjectSource();

			WebObjectInfo parentObjInfo = _m_objectSource.getObject( sParentFolderId, EnumDSSXMLObjectTypes.DssXmlTypeFolder );

			parentObjInfo.setFlags( parentObjInfo.getFlags() | EnumDSSXMLObjectFlags.DssXmlObjectFindHidden );

			parentObjInfo.populate();

			WebFolder folderToSearch = (WebFolder) parentObjInfo;

			WebFolder objectsList = folderToSearch.findTypedObjects( objTypes );

			if ( !objectsList.isEmpty() )
			{
				WebObjectInfo objInfo = null;

				for ( int i = 0; i < objectsList.size(); i++ )
				{
					objInfo = objectsList.get(i);

					if ( objInfo.getType() == EnumDSSXMLObjectTypes.DssXmlTypeReportDefinition &&
							objInfo.getSubType() == EnumDSSXMLObjectSubTypes.DssXmlSubTypeReportEmmaCube)
					{

						System.out.println("call getReportProperties");

						getReportProperties( objInfo.getID() );

						System.out.println("finish getReportProperties");


					}
					else if ( objInfo.getType() == EnumDSSXMLObjectTypes.DssXmlTypeFolder )
					{
						searchReportObjects( objInfo.getID() );
					}

				}
			}
		}
		catch ( Exception e )
		{
			System.out.println( "[E][searchReportObjects] " + e.getMessage() );
		}
	}

	private static String getFolderPath( WebObjectInfo objInfo )
	{
		String path = "";

		SimpleList list = objInfo.getAncestors();

		for (int j = 1; j < objInfo.getAncestors().size();  j++)
		{
			String p = ((WebObjectInfo) list.item(j)).getName();

			path = path + sPathSeparator + p;
		}
		return path;
	}


	private static void getReportProperties( String sObjID )
	{
		try
		{

			_m_objectSource = _m_objectFactory.getObjectSource();

			WebObjectInfo objInfo = _m_objectSource.getObject( sObjID, EnumDSSXMLObjectTypes.DssXmlTypeReportDefinition );

			objInfo.populate();

			nodeInfo = new ListDataImportCubeProperties.NodeInfo();

			nodeInfo.id =  objInfo.getID();

			nodeInfo.name =  objInfo.getName();

			sPath = getFolderPath( objInfo );

			nodeInfo.parentPath = sPath;

			nodeInfo.absolutePath = sPath + sPathSeparator + nodeInfo.name;

			nodeInfo.isHidden = objInfo.isHidden();

			nodeInfo.hiddenLabel = "";

			if( nodeInfo.isHidden )
			{
				nodeInfo.hiddenLabel = sHiddenLabel;
			}

			nodeInfo.modificationTime = changeDateFormat( objInfo.getModificationTime(), "MM/dd/yy", "yyyy/MM/dd" );

			if( objInfo.getSubType() == EnumDSSXMLObjectSubTypes.DssXmlSubTypeReportCube )
			{
				nodeInfo.type = "Cube";
			}

			if ( _m_debugMode )
			{
				WebPropertyGroup webPropertyGroup = objInfo.getPropertySets();

				for ( int i = 0; i < webPropertyGroup.size(); i++ )
				{
					WebPropertySet webPropertySet = webPropertyGroup.get( i );

					for ( int n = 0; n < webPropertySet.size(); n++ )
					{
						debug( "[D][getReportProperties]" + sColumnSeparator + webPropertySet.getName() + sColumnSeparator + webPropertySet.get(n).getName()
								+ sColumnSeparator + webPropertySet.get(n).getValue() );
					}
				}
			}

			WebReportSource repSource = _m_objectFactory.getReportSource();

			//repSource.setExecutionFlags( EnumDSSXMLExecutionFlags.DssXmlExecutionResolve );
			//repSource.setExecutionFlags( EnumDSSXMLExecutionFlags.DssXmlExecutionUseCache );
			//repSource.setExecutionFlags( EnumDSSXMLExecutionFlags.DssXmlExecutionUpdateCache );
			//repSource.setExecutionFlags( EnumDSSXMLExecutionFlags.DssXmlExecutionDefaultPrompt );
			//repSource.setResultFlags( EnumDSSXMLResultFlags.DssXmlResultXmlSQL );
			//repSource.setExecutionFlags( EnumDSSXMLExecutionFlags.DssXmlExecutionGenerateSQL );
			repSource.setExecutionFlags( EnumDSSXMLExecutionFlags.DssXmlExecutionNoAction );

			WebReportInstance repInstance = repSource.getNewInstance( sObjID );
			//repInstance.setAsync( false );
			repInstance.setAsync( true );
			repInstance.setMaxWait( 12000000 );
			repInstance.setPollingFrequency( 250 );

			//System.out.println( "repInstance.status1:" + repInstance.getStatus()) ;
			//Thread.sleep(10000); // 10秒

			while ( (repInstance.getEmmaSourceTablesAsJSON()).equals("") )
			{
				System.out.println( "getEmmaSourceTablesAsJSON() is blank. so sleep 2 sec" ) ;
				Thread.sleep(2000); // 2秒間だけ処理を止める

				System.out.println(  "repInstance.datasource:" + repInstance.getEmmaSourceTablesAsJSON() );
			}

			//System.out.println( "repInstance.status3:" + repInstance.getStatus()) ;

			String sJson = repInstance.getEmmaSourceTablesAsJSON();

			System.out.println( objInfo.getID() + sColumnSeparator +  sJson );

	        String target = "\"dbrid\":\"";
	        int startIndex = sJson.indexOf(target) + target.length();
	        int endIndex = sJson.indexOf("\"", startIndex);

	        nodeInfo.datasource1 = sJson.substring(startIndex, endIndex);

			System.out.println( objInfo.getID() + sColumnSeparator +  nodeInfo.datasource1 );


	        target = "\"srcid\":\"";
	        startIndex = sJson.indexOf(target) + target.length();
	        endIndex = sJson.indexOf("\"", startIndex);

	        nodeInfo.datasource2= sJson.substring(startIndex, endIndex);

			System.out.println( objInfo.getID() + sColumnSeparator +  nodeInfo.datasource2 );

	        target = "\"url\":\"";
	        startIndex = sJson.indexOf(target) + target.length();
	        endIndex = sJson.indexOf("\"", startIndex);

	        nodeInfo.datasource3 = sJson.substring(startIndex, endIndex);

			System.out.println( objInfo.getID() + sColumnSeparator +  nodeInfo.datasource3 );


			repInstance = null;
			repSource = null;

			exportNodeInfo( nodeInfo );

			_m_objectCount++;
		}
		catch (Exception e)
		{
			//e.printStackTrace();

			System.out.println( "[E][getReportProperties] " + nodeInfo.absolutePath );

			System.out.println("[E][getReportProperties] " + e.getMessage() );
		}
	}






	private static void openSession( String servername, int port, int authmode, String projectname, String username, String password )
	{
		if( _m_serverSession == null )
		{
			_m_objectFactory = WebObjectsFactory.getInstance();

			_m_serverSession = _m_objectFactory.getIServerSession();

			_m_serverSession.setServerName( servername );

			_m_serverSession.setServerPort( port );

			_m_serverSession.setProjectName( projectname );

			_m_serverSession.setAuthMode( authmode );

			_m_serverSession.setLogin( username );

			_m_serverSession.setPassword( password );

			_m_serverSession.setApplicationType(EnumDSSXMLApplicationType.DssXmlApplicationCustomApp);

			System.out.println( "[I][openSession] User [" + username + "] logged into [" + servername + "]." );
			do
			{
				if (authmode == EnumDSSXMLAuthModes.DssXmlAuthStandard)
				{
					System.out.println( "[I][openSession] AuthMode is [Standard authentication]." );

					break;
				}
				if (authmode == EnumDSSXMLAuthModes.DssXmlAuthNTCredential)
				{
					System.out.println( "[I][openSession] AuthMode is [Windows authentication]." );

					break;
				}
				if (authmode == EnumDSSXMLAuthModes.DssXmlAuthPassThrough)
				{
					System.out.println( "[I][openSession] AuthMode is [Pass Through authentication]." );

					break;
				}
				if (authmode == EnumDSSXMLAuthModes.DssXmlAuthLDAP)
				{
					System.out.println( "[I][openSession] AuthMode is [LDAP authentication]." );

					break;
				}
				if (authmode == EnumDSSXMLAuthModes.DssXmlAuthWarehousePassthrough)
				{
					System.out.println( "[I][openSession] AuthMode is [Database authentication]." );

				}
				if (authmode == EnumDSSXMLAuthModes.DssXmlAuthTrusted)
				{
					System.out.println( "[I][openSession] AuthMode is [Trusted authentication]." );

					break;
				}
				if (authmode == EnumDSSXMLAuthModes.DssXmlAuthIntegrated)
				{
					System.out.println( "[I][openSession] AuthMode is [Integrated authentication]." );

					break;
				}

			} while(false);
		}
	}

	private static void closeSession( WebIServerSession serverSession )
	{
		try
		{
			serverSession.closeSession();
		}
		catch( WebObjectsException e )
		{
			System.out.println( "[E][closeSession] " + e.getMessage() );

			return;
		}

		System.out.println("[I][closeSession] Session had been closed.");
	}


	private static void openOutputStream()
	{
		try
		{
			if( _m_bw != null )
			{
				throw( new Exception( "[E][openOutputStream] File is alraedy opened." ) );
			}

			File outputFile = new File( sOutputFileName );

			_m_bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outputFile, true), "UTF-8"));
		}
		catch( Exception e )
		{
			e.printStackTrace();
		}
	}

	private static void closeOutputStream()
	{
		try
		{
			if( _m_bw != null )
			{
				_m_bw.close();

				_m_bw = null;
			}
		}
		catch( Exception e )
		{
			e.printStackTrace();
		}
	}

	private static void openPropertyFile()
	{
		try
		{
			InputStreamReader isr = new InputStreamReader(new FileInputStream(new File(sMsgFile)), "UTF-8");

			_m_messageProperties.load(isr);

			isr.close();
		}
		catch( Exception e )
		{
			System.out.println( "[E][openPropertyFile] " + e.getMessage() );
		}

	}

	private static String changeDateFormat( String value, String bformat, String aformat )
	{
		SimpleDateFormat bsdf = new SimpleDateFormat( bformat );

		SimpleDateFormat asdf = new SimpleDateFormat( aformat );

		try
		{
			return( asdf.format( bsdf.parse( value ) ) );
		}
		catch( ParseException e )
		{
			return( null );
		}
	}

	private static void exportNodeInfo( ListDataImportCubeProperties.NodeInfo nodeInfo )
	{
		try
		{
			if( nodeInfo == null )
			{
				throw( new Exception( "Invalid argument. 'nodeInfo' is null." ) );
			}



					StringBuffer line = new StringBuffer();

					// ID
					line.append( sColumnDelimiter + nodeInfo.id + sColumnDelimiter );

					// Path
					line.append( sColumnSeparator );

					line.append( sColumnDelimiter + nodeInfo.parentPath + sColumnDelimiter );

					// Name
					line.append( sColumnSeparator );

					line.append( sColumnDelimiter + nodeInfo.name + sColumnDelimiter );

					// Hidden
					line.append( sColumnSeparator );

					line.append( sColumnDelimiter + nodeInfo.hiddenLabel + sColumnDelimiter );

					// Last modification time
					line.append( sColumnSeparator );

					line.append( sColumnDelimiter + nodeInfo.modificationTime + sColumnDelimiter );

					line.append( sColumnSeparator );

					line.append( sColumnDelimiter + nodeInfo.datasource1 + sColumnDelimiter );

					line.append( sColumnSeparator );

					line.append( sColumnDelimiter + nodeInfo.datasource2 + sColumnDelimiter );

					line.append( sColumnSeparator );

					line.append( sColumnDelimiter + nodeInfo.datasource3 + sColumnDelimiter );

					_m_bw.write( line.toString() );

					_m_bw.newLine();

					_m_bw.flush();


			System.out.println( "[I][exportNodeInfo] " + nodeInfo.absolutePath );
		}
		catch ( Exception e )
		{
			System.out.println( "[E][exportNodeInfos] " + e.getMessage() );
		}
	}

	 public static boolean f_a()
     {
            boolean r = false;

            Calendar c1 = Calendar.getInstance( Locale.JAPAN );

            Calendar c2 = Calendar.getInstance( Locale.JAPAN );

            // 期限を設定
            c2.set( 2023, Calendar.JANUARY, 1, 0, 0, 0 );

            if( c1.before( c2 ) ) r = true;

            return( r );
     }

	private static void debug( String message ) throws Exception
	{
		if( _m_debugMode )
		{
			//System.out.println( message );

			PrintStream out = new PrintStream(System.out, true, "UTF-8");
			out.println( message );
		}
	}

}
