import requests
import json
from urllib3.exceptions import InsecureRequestWarning
import os
from dotenv import load_dotenv

# Suppress only the single warning from urllib3 needed.
requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)

# Load environment variables from .env file
load_dotenv()

def get_auth_token(session, base_url, username, password):
    """Get authentication token from MicroStrategy server"""
    try:
        # Login endpoint
        login_url = f"{base_url}/auth/login"
        
        # Login payload
        payload = {
            "username": username,
            "password": password,
            "loginMode": 1
        }
        
        # Make login request
        response = session.post(login_url, json=payload, verify=False)
        response.raise_for_status()
        
        # Get auth token from response headers
        auth_token = response.headers.get('X-MSTR-AuthToken')
        return auth_token
    except Exception as e:
        print(f"Error getting auth token: {str(e)}")
        return None

def get_user_info(session, base_url, auth_token, user_id):
    """Get user information using the REST API"""
    try:
        # User info endpoint
        user_url = f"{base_url}/users/{user_id}"
        
        # Headers with auth token
        headers = {
            'X-MSTR-AuthToken': auth_token,
            'Content-Type': 'application/json'
        }
        
        # Make request to get user info
        response = session.get(user_url, headers=headers, verify=False)
        response.raise_for_status()
        
        # Parse and return user info
        user_info = response.json()
        return user_info
    except Exception as e:
        print(f"Error getting user info: {str(e)}")
        return None

def main():
    # MicroStrategy server configuration
    base_url = 'http://rhanvm2019:8080/MicroStrategyLibrary/api'
    username = 'administrator'
    password = ''
    user_id = '658F69C8431E03DCF6538CB0C03D75D0'
    
    print("Connecting to MicroStrategy server...")
    
    # Create a session
    session = requests.Session()
    
    # Get authentication token
    auth_token = get_auth_token(session, base_url, username, password)
    if not auth_token:
        print("Failed to get authentication token")
        return
    
    print("Successfully authenticated")
    
    # Get user information
    user_info = get_user_info(session, base_url, auth_token, user_id)
    if user_info:
        print("\nUser Information:")
        print(json.dumps(user_info, ensure_ascii=False, indent=2))
    else:
        print("Failed to get user information")

if __name__ == "__main__":
    main() 