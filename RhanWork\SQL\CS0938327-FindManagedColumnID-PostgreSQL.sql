-- PostgreSQL Version: Find Column Dependencies for Metrics in Report: CS0938327FFReport2
-- Database: poc_metadata (PostgreSQL)
-- Report: CS0938327FFReport2

-- Main Query: Get all column objects depended by metrics in the report
WITH report_metrics AS (
    -- Get all metrics used in the specified report
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM public.dssobjects rep
    INNER JOIN public.dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN public.dssobjects met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3      -- Report object type
        AND rep.object_subtype = 768 -- Report subtype
        AND met.object_type = 4      -- Metric object type
        AND met.object_subtype = 1024 -- Metric subtype
),
column_dependencies AS (
    -- Get all column dependencies for each metric
    SELECT DISTINCT
        rm.metric_id,
        rm.metric_name,
        dep.child_object_id as column_id,
        col.object_name as column_name,
        col.object_type,
        col.object_subtype
    FROM report_metrics rm
    INNER JOIN public.dssobjdep dep ON rm.metric_id = dep.parent_object_id
    INNER JOIN public.dssobjects col ON dep.child_object_id = col.object_id
    WHERE col.object_type IN (12, 13) -- Column and Attribute object types
)
SELECT 
    cd.metric_name,
    cd.column_id,
    cd.column_name,
    cd.object_type,
    cd.object_subtype,
    CASE 
        WHEN cd.object_type = 12 THEN 'Column'
        WHEN cd.object_type = 13 THEN 'Attribute'
        ELSE 'Other'
    END as object_type_description
FROM column_dependencies cd
ORDER BY cd.metric_name, cd.column_name;

-- Extended Query: Include physical table information
WITH report_metrics AS (
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM public.dssobjects rep
    INNER JOIN public.dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN public.dssobjects met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3
        AND rep.object_subtype = 768
        AND met.object_type = 4
        AND met.object_subtype = 1024
)
SELECT DISTINCT
    rm.metric_name,
    col.object_id as column_id,
    col.object_name as column_name,
    col.object_type,
    col.object_subtype,
    attr.data_type,
    attr.column_length,
    pt.table_name as source_table,
    pc.column_name as physical_column_name
FROM report_metrics rm
INNER JOIN public.dssobjdep dep ON rm.metric_id = dep.parent_object_id
INNER JOIN public.dssobjects col ON dep.child_object_id = col.object_id
LEFT JOIN public.dssobjattr attr ON col.object_id = attr.object_id
LEFT JOIN public.dssobjdep col_dep ON col.object_id = col_dep.parent_object_id
LEFT JOIN public.dssphysicalcolumn pc ON col_dep.child_object_id = pc.column_id
LEFT JOIN public.dssphysicaltable pt ON pc.table_id = pt.table_id
WHERE col.object_type IN (12, 13)
ORDER BY rm.metric_name, col.object_name;

-- Summary Query: Count of column dependencies per metric
WITH report_metrics AS (
    SELECT DISTINCT
        dep.child_object_id as metric_id,
        met.object_name as metric_name
    FROM public.dssobjects rep
    INNER JOIN public.dssobjdep dep ON rep.object_id = dep.parent_object_id
    INNER JOIN public.dssobjects met ON dep.child_object_id = met.object_id
    WHERE rep.object_name = 'CS0938327FFReport2'
        AND rep.object_type = 3
        AND rep.object_subtype = 768
        AND met.object_type = 4
        AND met.object_subtype = 1024
),
metric_column_deps AS (
    SELECT DISTINCT
        rm.metric_id,
        rm.metric_name,
        dep.child_object_id as column_id
    FROM report_metrics rm
    INNER JOIN public.dssobjdep dep ON rm.metric_id = dep.parent_object_id
    INNER JOIN public.dssobjects col ON dep.child_object_id = col.object_id
    WHERE col.object_type IN (12, 13)
)
SELECT 
    mcd.metric_name,
    COUNT(DISTINCT mcd.column_id) as column_count,
    STRING_AGG(col.object_name, ', ' ORDER BY col.object_name) as column_names
FROM metric_column_deps mcd
INNER JOIN public.dssobjects col ON mcd.column_id = col.object_id
GROUP BY mcd.metric_name
ORDER BY column_count DESC, mcd.metric_name;

-- Verification Query: Check if report exists
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype,
    date_created,
    date_modified
FROM public.dssobjects 
WHERE object_name = 'CS0938327FFReport2'
    AND object_type = 3
    AND object_subtype = 768;
