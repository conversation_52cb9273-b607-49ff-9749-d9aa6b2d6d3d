from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time

# Step 1: Set up the WebDriver
driver = webdriver.Chrome()  # Replace with `webdriver.Firefox()` if using Firefox
### driver.minimize_window()

try:
    # Step 2: Open the URL
    driver.get("https://sup-w-006478.labs.microstrategy.com:8443/MicroStrategy/servlet/mstrWeb")  # Replace with your URL
    time.sleep(2)  # Wait for the page to load
    
    submit_button = driver.find_element(By.ID, "details-button")  # Replace with the actual field's name or ID
    submit_button.click()
    time.sleep(3) 
    
    submit_button = driver.find_element(By.ID, "proceed-link")  # Replace with the actual field's name or ID
    submit_button.click()
    time.sleep(5) 
    
    # Step 3: Locate the form fields and fill them
    # Example: Fill a text input field with the name 'username'
    username_field = driver.find_element(By.NAME, "email")  # Replace with the actual field's name or ID
    username_field.send_keys("<EMAIL>")

    # Step 4: Locate and press the "OK" or "Submit" button
    submit_button = driver.find_element(By.XPATH, "//button[@type='submit']")  # Replace with the actual button's name or ID
    submit_button.click()
    time.sleep(5)  # Wait for the page to load
    
    # Example: Fill a password field with the name 'password'
    password_field = driver.find_element(By.NAME, "password")  # Replace with the actual field's name or ID
    password_field.send_keys("M$tr!234")

    '''
    # Example: Select a dropdown (if applicable)
    dropdown = driver.find_element(By.NAME, "country")  # Replace with the actual field's name
    dropdown.click()
    time.sleep(1)  # Wait for options to load
    dropdown_option = driver.find_element(By.XPATH, "//option[text()='United States']")  # Replace with the appropriate value
    dropdown_option.click()
    '''
    
    # Step 4: Locate and press the "OK" or "Submit" button
    submit_button = driver.find_element(By.XPATH, "//button[@type='submit']")  # Replace with the actual button's name or ID
    submit_button.click()

    # Optional: Wait for the next page to load
    time.sleep(5)
    print("Login is successful")
    
except Exception as e:
    print("Error occurred during the login")
finally:
    # Step 5: Close the browser
    driver.quit()
