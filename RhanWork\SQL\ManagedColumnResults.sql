-- FINAL RESULTS: Managed Column Objects for Report CS0938327FFReport2
-- Database: poc_metadata (PostgreSQL) - Server: 10.27.72.202:5432
-- Analysis completed successfully

-- =====================================================================================
-- SUMMARY OF FINDINGS
-- =====================================================================================

/*
REPORT ANALYSIS RESULTS:
- Report Name: CS0938327FFReport2
- Report ID: 0E9F35104A20F9EB83AAC9B7B9C613C5
- Total Metrics in Report: 2
- Total Managed Columns Found: 2

METRICS FOUND:
1. IJ期初金額＿前期1年累計 (ID: 3FCDB9CA41EB46AAAFD692528B54AA72)
2. IJ期初本数＿前期1年累計 (ID: B2627BE04448440B9AA772CE475D2C2A)

MANAGED COLUMNS FOUND:
1. IJ期初金額＿前期1年累計 (ID: 5079544DF950476FBD5833798EDCE295)
   - Object Type: 26 (Managed Column)
   - Subtype: 6656 (0x1A00)
   - Extended Type: 3
   - Used by Metric: IJ期初金額＿前期1年累計

2. IJ期初本数＿前期1年累計 (ID: 6E25F8CA26BC48D0ADBDD366468BE365)
   - Object Type: 26 (Managed Column)
   - Subtype: 6656 (0x1A00)
   - Extended Type: 3
   - Used by Metric: IJ期初本数＿前期1年累計

RELATIONSHIP PATTERN:
Each metric has a 1:1 relationship with a managed column of the same name.
Both managed columns have identical characteristics:
- object_type = 26
- subtype = 6656 (0x1A00)
- extended_type = 3
- Same parent_id: EE9BD70043F0F571B38E43BF19B9301A
- Same owner_id: 54F3D26011D2896560009A8E67019608
*/

-- =====================================================================================
-- VERIFIED QUERIES FOR MANAGED COLUMN EXTRACTION
-- =====================================================================================

-- Query 1: Get all metrics in the report
SELECT DISTINCT
    m.object_id as metric_id,
    m.object_name as metric_name,
    m.object_type,
    m.subtype
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
WHERE r.object_name = 'CS0938327FFReport2'
    AND r.object_type = 3
    AND r.subtype = 768
    AND m.object_type = 4
    AND m.subtype = 1024
ORDER BY m.object_name;

-- Query 2: Get managed columns for each metric
WITH report_metrics AS (
    SELECT DISTINCT
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3
        AND r.subtype = 768
        AND m.object_type = 4
        AND m.subtype = 1024
)
SELECT DISTINCT
    rm.metric_name,
    mc.object_id as managed_column_id,
    mc.object_name as managed_column_name,
    mc.object_type,
    mc.subtype,
    mc.extended_type,
    mc.description,
    mc.create_time,
    mc.mod_time,
    mc.parent_id,
    mc.owner_id
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo mc ON d.depn_objid = mc.object_id
WHERE mc.object_type = 26  -- Managed columns
    AND mc.subtype = 6656  -- 0x1A00
    AND mc.extended_type = 3
ORDER BY rm.metric_name, mc.object_name;

-- Query 3: Detailed managed column information
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    extended_type,
    description,
    abbreviation,
    create_time,
    mod_time,
    parent_id,
    owner_id,
    object_state,
    hidden,
    locale
FROM dssmdobjinfo 
WHERE object_id IN (
    '5079544DF950476FBD5833798EDCE295',  -- IJ期初金額＿前期1年累計
    '6E25F8CA26BC48D0ADBDD366468BE365'   -- IJ期初本数＿前期1年累計
)
ORDER BY object_name;

-- Query 4: Verify dependency relationships
SELECT 
    d.object_id as metric_id,
    m.object_name as metric_name,
    d.depn_objid as managed_column_id,
    mc.object_name as managed_column_name,
    mc.object_type,
    mc.subtype,
    mc.extended_type
FROM dssmdobjdepn d
INNER JOIN dssmdobjinfo m ON d.object_id = m.object_id
INNER JOIN dssmdobjinfo mc ON d.depn_objid = mc.object_id
WHERE d.object_id IN (
    '3FCDB9CA41EB46AAAFD692528B54AA72',  -- Metric: IJ期初金額＿前期1年累計
    'B2627BE04448440B9AA772CE475D2C2A'   -- Metric: IJ期初本数＿前期1年累計
)
    AND mc.object_type = 26
    AND mc.subtype = 6656
    AND mc.extended_type = 3
ORDER BY m.object_name;

-- Query 5: Summary statistics
SELECT 
    'Total Managed Columns' as description,
    COUNT(DISTINCT mc.object_id) as count
FROM dssmdobjdepn d
INNER JOIN dssmdobjinfo mc ON d.depn_objid = mc.object_id
WHERE d.object_id IN (
    '3FCDB9CA41EB46AAAFD692528B54AA72',
    'B2627BE04448440B9AA772CE475D2C2A'
)
    AND mc.object_type = 26
    AND mc.subtype = 6656
    AND mc.extended_type = 3

UNION ALL

SELECT 
    'Total Metrics Using Managed Columns' as description,
    COUNT(DISTINCT d.object_id) as count
FROM dssmdobjdepn d
INNER JOIN dssmdobjinfo mc ON d.depn_objid = mc.object_id
WHERE d.object_id IN (
    '3FCDB9CA41EB46AAAFD692528B54AA72',
    'B2627BE04448440B9AA772CE475D2C2A'
)
    AND mc.object_type = 26
    AND mc.subtype = 6656
    AND mc.extended_type = 3;

-- =====================================================================================
-- ADDITIONAL ANALYSIS QUERIES
-- =====================================================================================

-- Query 6: Check for other managed columns in the database with same characteristics
SELECT 
    COUNT(*) as total_managed_columns_in_db,
    COUNT(CASE WHEN subtype = 6656 THEN 1 END) as subtype_6656_count,
    COUNT(CASE WHEN extended_type = 3 THEN 1 END) as extended_type_3_count,
    COUNT(CASE WHEN subtype = 6656 AND extended_type = 3 THEN 1 END) as matching_criteria_count
FROM dssmdobjinfo
WHERE object_type = 26;

-- Query 7: Find parent object information
SELECT 
    p.object_id as parent_id,
    p.object_name as parent_name,
    p.object_type as parent_type,
    p.subtype as parent_subtype,
    COUNT(*) as managed_columns_count
FROM dssmdobjinfo mc
INNER JOIN dssmdobjinfo p ON mc.parent_id = p.object_id
WHERE mc.object_id IN (
    '5079544DF950476FBD5833798EDCE295',
    '6E25F8CA26BC48D0ADBDD366468BE365'
)
GROUP BY p.object_id, p.object_name, p.object_type, p.subtype;

-- =====================================================================================
-- EXECUTION RESULTS (from actual database queries):
-- =====================================================================================

/*
METRICS IN REPORT CS0938327FFReport2:
┌──────────────────────────────────┬─────────────────────────┬─────────────┬─────────┐
│            metric_id             │       metric_name       │ object_type │ subtype │
├──────────────────────────────────┼─────────────────────────┼─────────────┼─────────┤
│ 3FCDB9CA41EB46AAAFD692528B54AA72 │ IJ期初金額＿前期1年累計 │           4 │    1024 │
│ B2627BE04448440B9AA772CE475D2C2A │ IJ期初本数＿前期1年累計 │           4 │    1024 │
└──────────────────────────────────┴─────────────────────────┴─────────────┴─────────┘

MANAGED COLUMNS USED BY THESE METRICS:
┌─────────────────────────┬──────────────────────────────────┬─────────────────────────┬─────────────┬─────────┬───────────────┐
│       metric_name       │       managed_column_id          │   managed_column_name   │ object_type │ subtype │ extended_type │
├─────────────────────────┼──────────────────────────────────┼─────────────────────────┼─────────────┼─────────┼───────────────┤
│ IJ期初金額＿前期1年累計 │ 5079544DF950476FBD5833798EDCE295 │ IJ期初金額＿前期1年累計 │          26 │    6656 │             3 │
│ IJ期初本数＿前期1年累計 │ 6E25F8CA26BC48D0ADBDD366468BE365 │ IJ期初本数＿前期1年累計 │          26 │    6656 │             3 │
└─────────────────────────┴──────────────────────────────────┴─────────────────────────┴─────────────┴─────────┴───────────────┘

SUMMARY:
- 2 metrics found in report CS0938327FFReport2
- 2 managed columns found (1:1 relationship with metrics)
- All managed columns have object_type=26, subtype=6656, extended_type=3
- Both managed columns share the same parent_id and owner_id
*/
