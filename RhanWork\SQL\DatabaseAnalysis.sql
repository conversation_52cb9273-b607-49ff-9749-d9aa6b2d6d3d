-- Database Analysis for MicroStrategy Metadata Tables
-- Database: poc_metadata (PostgreSQL)
-- Purpose: Analyze table structures and relationships

-- =====================================================================================
-- STEP 1: ANALYZE TABLE STRUCTURES
-- =====================================================================================

-- Get detailed structure of dssmdobjinfo table
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'dssmdobjinfo'
ORDER BY ordinal_position;

-- Get detailed structure of dssmdobjdepn table
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'dssmdobjdepn'
ORDER BY ordinal_position;

-- Get detailed structure of dssmdobjdefn table (object definitions)
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'dssmdobjdefn'
ORDER BY ordinal_position;

-- =====================================================================================
-- STEP 2: SAMPLE DATA ANALYSIS
-- =====================================================================================

-- Sample data from dssmdobjinfo to understand data format
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype,
    date_created,
    date_modified
FROM dssmdobjinfo
ORDER BY object_type, object_subtype, object_name
LIMIT 20;

-- Sample data from dssmdobjdepn to understand dependency structure
SELECT 
    parent_object_id,
    child_object_id,
    dependency_type
FROM dssmdobjdepn
LIMIT 20;

-- =====================================================================================
-- STEP 3: OBJECT TYPE ANALYSIS
-- =====================================================================================

-- Analyze all object types and subtypes in the database
SELECT 
    object_type,
    object_subtype,
    COUNT(*) as count,
    MIN(object_name) as sample_name_1,
    MAX(object_name) as sample_name_2
FROM dssmdobjinfo
GROUP BY object_type, object_subtype
ORDER BY object_type, object_subtype;

-- Find reports specifically (object_type = 3)
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype,
    date_created
FROM dssmdobjinfo
WHERE object_type = 3
ORDER BY object_name;

-- Find metrics specifically (object_type = 4)
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype,
    date_created
FROM dssmdobjinfo
WHERE object_type = 4
ORDER BY object_name;

-- =====================================================================================
-- STEP 4: SEARCH FOR TARGET REPORT
-- =====================================================================================

-- Search for the specific report with various patterns
SELECT 
    object_id,
    object_name,
    object_type,
    object_subtype,
    date_created
FROM dssmdobjinfo
WHERE object_name ILIKE '%CS0938327%'
   OR object_name ILIKE '%938327%'
   OR object_name ILIKE '%FFReport%'
   OR object_name ILIKE '%Report2%'
ORDER BY object_name;

-- =====================================================================================
-- STEP 5: DEPENDENCY RELATIONSHIP ANALYSIS
-- =====================================================================================

-- Analyze dependency patterns between different object types
SELECT 
    parent_obj.object_type as parent_type,
    parent_obj.object_subtype as parent_subtype,
    child_obj.object_type as child_type,
    child_obj.object_subtype as child_subtype,
    COUNT(*) as relationship_count
FROM dssmdobjdepn dep
INNER JOIN dssmdobjinfo parent_obj ON dep.parent_object_id = parent_obj.object_id
INNER JOIN dssmdobjinfo child_obj ON dep.child_object_id = child_obj.object_id
GROUP BY parent_obj.object_type, parent_obj.object_subtype, 
         child_obj.object_type, child_obj.object_subtype
ORDER BY parent_obj.object_type, child_obj.object_type;

-- =====================================================================================
-- STEP 6: IDENTIFY COLUMN-RELATED OBJECTS
-- =====================================================================================

-- Find objects that might be columns or attributes
SELECT 
    object_type,
    object_subtype,
    COUNT(*) as count,
    STRING_AGG(object_name, ', ') as sample_names
FROM dssmdobjinfo
WHERE object_type IN (1, 12, 13, 14, 15)  -- Common column/attribute types
GROUP BY object_type, object_subtype
ORDER BY object_type, object_subtype;

-- =====================================================================================
-- STEP 7: FOREIGN KEY ANALYSIS
-- =====================================================================================

-- Check for foreign key constraints
SELECT
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND tc.table_name IN ('dssmdobjinfo', 'dssmdobjdepn', 'dssmdobjdefn')
ORDER BY tc.table_name, kcu.column_name;

-- =====================================================================================
-- STEP 8: INDEX ANALYSIS
-- =====================================================================================

-- Check indexes on key tables
SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
    AND tablename IN ('dssmdobjinfo', 'dssmdobjdepn', 'dssmdobjdefn')
ORDER BY tablename, indexname;
