-- PostgreSQL version of V_S_SUPPLY_ABCD_KI view
-- Converted from Teradata SQL

CREATE VIEW V_S_SUPPLY_ABCD_KI(
  V_CODE_KOKYAKUJIGYOSYO
 ,V_CODE_KOKYAKU
 ,V_KANJ_KOKYAKUMEI
 ,V_CODE_YUBIN
 ,V_KANJ_JYUSHO
 ,V_DATE_URIAGE_KI
 ,V_DATE_NEN
 ,V_KBN_NEWJG
 ,V_NUM_IJ_ABCD04
 ,V_NUM_IJ_ABCD05
 ,V_NUM_IJ_ABCD06
 ,V_NUM_IJ_ABCD07
 ,V_NUM_IJ_ABCD08
 ,V_NUM_IJ_ABCD09
 ,V_NUM_IJ_ABCD10
 ,V_NUM_IJ_ABCD11
 ,V_NUM_IJ_ABCD12
 ,V_NUM_IJ_ABCD01
 ,V_NUM_IJ_ABCD02
 ,V_NUM_IJ_ABCD03
 ,V_NUM_IJ_KINGAKU
 ,V_NUM_IJ_SURYO
 ,V_NUM_IJ_SURYO_1000
 ,V_NUM_RG_ABCD04
 ,V_NUM_RG_ABCD05
 ,V_NUM_RG_ABCD06
 ,V_NUM_RG_ABCD07
 ,V_NUM_RG_ABCD08
 ,V_NUM_RG_ABCD09
 ,V_NUM_RG_ABCD10
 ,V_NUM_RG_ABCD11
 ,V_NUM_RG_ABCD12
 ,V_NUM_RG_ABCD01
 ,V_NUM_RG_ABCD02
 ,V_NUM_RG_ABCD03
 ,V_NUM_RG_KINGAKU
 ,V_NUM_IJ_ABCD_NOW
 ,V_NUM_IJ_KINGAKU_NOW
 ,V_NUM_IJ_SURYO_NOW
 ,V_NUM_IJ_SURYO_1000_NOW
 ,V_NUM_RG_ABCD_NOW
 ,V_NUM_RG_KINGAKU_NOW
)
AS SELECT 
  S_SUPPLY_ABCD_KI.S_CODE_KOKYAKUJIGYOSYO
 ,S_SUPPLY_ABCD_KI.S_CODE_KOKYAKU
 ,S_SUPPLY_ABCD_KI.S_KANJ_KOKYAKUMEI
 ,S_SUPPLY_ABCD_KI.S_CODE_YUBIN
 ,S_SUPPLY_ABCD_KI.S_KANJ_JYUSHO
 ,S_SUPPLY_ABCD_KI.S_DATE_URIAGE_KI
 ,CASE 
    WHEN SUBSTRING(TO_CHAR(CURRENT_DATE, 'YYYYMM'), 5, 2) < '04' 
    THEN EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER - 1954 - S_SUPPLY_ABCD_KI.S_DATE_URIAGE_KI 
    WHEN SUBSTRING(TO_CHAR(CURRENT_DATE, 'YYYYMM'), 5, 2) >= '04' 
    THEN EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER - 1953 - S_SUPPLY_ABCD_KI.S_DATE_URIAGE_KI 
  END AS "年前"
 ,S_SUPPLY_ABCD_KI.S_KBN_NEWJG
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD04
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD05
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD06
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD07
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD08
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD09
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD10
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD11
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD12
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD01
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD02
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD03
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_KINGAKU
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_SURYO
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_SURYO_1000
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD04
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD05
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD06
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD07
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD08
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD09
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD10
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD11
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD12
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD01
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD02
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD03
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_KINGAKU
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_ABCD_NOW
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_KINGAKU_NOW
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_SURYO_NOW
 ,S_SUPPLY_ABCD_KI.S_NUM_IJ_SURYO_1000_NOW
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_ABCD_NOW
 ,S_SUPPLY_ABCD_KI.S_NUM_RG_KINGAKU_NOW
FROM
  S_SUPPLY_ABCD_KI;
