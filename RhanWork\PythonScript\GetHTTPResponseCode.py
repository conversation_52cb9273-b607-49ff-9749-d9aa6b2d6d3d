import requests

# MicroStrategy Web login URL
#login_url = 'http://10.27.72.202:8080/MicroStrategy/servlet/mstrWeb'

login_url = 'http://rhanvm2019:8080/MicroStrategy/servlet/taskAdmin?taskId=login&taskEnv=xml&taskContentType=xml&server=rhanvm2019&project=MicroStrategy+Tutorial&userid=administrator&password='


# Login payload with the necessary parameters
login_payload = {
    'Username': 'admin',
    'Password': ''
}

# Start a session
r = requests.get(login_url)
print(r.text)  # To check if successfully logged in
#print(r.status_code)

'''
# Check if login was successful by looking for a known element on the page
task_admin_url = 'http://10.27.72.202:8080/MicroStrategy/servlet/mstrWeb?page=Main&evt=3010'
task_admin_response = session.get(task_admin_url)
print(task_admin_response.text)
'''