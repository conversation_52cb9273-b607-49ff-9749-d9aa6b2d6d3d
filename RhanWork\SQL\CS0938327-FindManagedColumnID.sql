-- DatabaseInfo:
-- server ip: ************
-- server port: 1433
-- server name: RhanVM2019
-- database name: poc_metadata
-- schema: public
-- username: mstr
-- password: zJ7MhP8mdRCI

-- Object Types:
-- metric object_type = 4 subtype = 1024
-- report object_type = 3 subtype = 768
-- managed column object_type = 26 subtype = 0x1A00, ExtType = 0x3

-- Parameter:
-- report name: CS0938327FFReport2

-- For example, the following metric included in report (CS0938327FFReport2), which depends on 1 managed column (object_type = 26, subtype = 0x1A00, ExtType = 0x3)
-- Info of Metric:
--    Name: IJ期初金額＿前期1年累計
--    object_id: 3FCDB9CA41EB46AAAFD692528B54AA72
-- Info of the dependent managed column
--    Name: IJ期初金額＿前期1年累計
--    object_id: 5079544DF950476FBD5833798EDCE295

My purpose is get all managed columns info included in the given report (CS0938327FFReport2),
but you need to find them through all the metric items included in the report.

-- =====================================================================================
-- SOLUTION: FIND ALL MANAGED COLUMNS USED BY METRICS IN THE REPORT
-- =====================================================================================

-- STEP 1: Get all metrics in the report
SELECT DISTINCT
    m.object_id as metric_id,
    m.object_name as metric_name,
    m.object_type,
    m.subtype
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
WHERE r.object_name = 'CS0938327FFReport2'
    AND r.object_type = 3
    AND r.subtype = 768
    AND m.object_type = 4
    AND m.subtype = 1024
ORDER BY m.object_name;

-- STEP 2: Find managed columns used by these metrics
WITH report_metrics AS (
    SELECT DISTINCT
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3
        AND r.subtype = 768
        AND m.object_type = 4
        AND m.subtype = 1024
)
SELECT DISTINCT
    rm.metric_name,
    mc.object_id as managed_column_id,
    mc.object_name as managed_column_name,
    mc.object_type,
    mc.subtype,
    mc.extended_type,
    mc.description,
    mc.create_time,
    mc.mod_time,
    mc.parent_id,
    mc.owner_id
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo mc ON d.depn_objid = mc.object_id
WHERE mc.object_type = 26  -- Managed columns
    AND mc.subtype = 6656  -- 0x1A00
    AND mc.extended_type = 3
ORDER BY rm.metric_name, mc.object_name;

-- STEP 3: Summary of managed columns found
SELECT
    COUNT(DISTINCT mc.object_id) as total_managed_columns,
    COUNT(DISTINCT rm.metric_id) as metrics_using_managed_columns
FROM (
    SELECT DISTINCT m.object_id as metric_id
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3 AND r.subtype = 768
        AND m.object_type = 4 AND m.subtype = 1024
) rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo mc ON d.depn_objid = mc.object_id
WHERE mc.object_type = 26 AND mc.subtype = 6656 AND mc.extended_type = 3;
