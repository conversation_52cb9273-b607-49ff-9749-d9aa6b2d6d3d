-- PostgreSQL version of S_SUPPLY_ABCD_KI table
-- Converted from Teradata SQL

CREATE TABLE S_SUPPLY_ABCD_KI (
    S_CODE_KOKYAKUJIGYOSYO VARCHAR(10),
    S_CODE_KOKYAKU VARCHAR(7),
    S_<PERSON><PERSON><PERSON>_KOKYAKUMEI VARCHAR(120),
    S_CODE_YUBIN VARCHAR(8),
    S_KANJ_JYUSHO VARCHAR(248),
    S_DATE_URIAGE_KI INTEGER,
    S_DATE_NEN INTEGER,
    S_KBN_NEWJG VARCHAR(10),
    S_NUM_IJ_ABCD04 VARCHAR(2),
    S_NUM_IJ_ABCD05 VARCHAR(2),
    S_NUM_IJ_ABCD06 VARCHAR(2),
    S_NUM_IJ_ABCD07 VARCHAR(2),
    S_NUM_IJ_ABCD08 VARCHAR(2),
    S_<PERSON><PERSON>_IJ_ABCD09 VARCHAR(2),
    <PERSON>_<PERSON>UM_IJ_ABCD10 VARCHAR(2),
    S_NUM_IJ_ABCD11 VARCHAR(2),
    S_NUM_IJ_ABCD12 VARCHAR(2),
    S_<PERSON>UM_IJ_ABCD01 VARCHAR(2),
    S_NUM_IJ_ABCD02 VARCHAR(2),
    S_NUM_IJ_ABCD03 VARCHAR(2),
    S_NUM_IJ_KINGAKU DECIMAL(13,0),
    S_NUM_IJ_SURYO DECIMAL(9,0),
    S_NUM_IJ_SURYO_1000 DECIMAL(9,0),
    S_NUM_RG_ABCD04 VARCHAR(2),
    S_NUM_RG_ABCD05 VARCHAR(2),
    S_NUM_RG_ABCD06 VARCHAR(2),
    S_NUM_RG_ABCD07 VARCHAR(2),
    S_NUM_RG_ABCD08 VARCHAR(2),
    S_NUM_RG_ABCD09 VARCHAR(2),
    S_NUM_RG_ABCD10 VARCHAR(2),
    S_NUM_RG_ABCD11 VARCHAR(2),
    S_NUM_RG_ABCD12 VARCHAR(2),
    S_NUM_RG_ABCD01 VARCHAR(2),
    S_NUM_RG_ABCD02 VARCHAR(2),
    S_NUM_RG_ABCD03 VARCHAR(2),
    S_NUM_RG_KINGAKU DECIMAL(13,0),
    S_NUM_IJ_ABCD_NOW VARCHAR(2),
    S_NUM_IJ_KINGAKU_NOW DECIMAL(13,0),
    S_NUM_IJ_SURYO_NOW DECIMAL(9,0),
    S_NUM_IJ_SURYO_1000_NOW DECIMAL(9,0),
    S_NUM_RG_ABCD_NOW VARCHAR(2),
    S_NUM_RG_KINGAKU_NOW DECIMAL(13,0),
    
    -- Primary key constraint (converted from Teradata UNIQUE PRIMARY INDEX)
    PRIMARY KEY (S_CODE_KOKYAKUJIGYOSYO, S_DATE_URIAGE_KI)
);

-- Optional: Create schema if it doesn't exist
-- CREATE SCHEMA IF NOT EXISTS risodb;

-- Optional: Add comments for documentation
COMMENT ON TABLE risodb.S_SUPPLY_ABCD_KI IS 'Supply ABCD KI data table - converted from Teradata';
COMMENT ON COLUMN risodb.S_SUPPLY_ABCD_KI.S_CODE_KOKYAKUJIGYOSYO IS 'Customer business office code';
COMMENT ON COLUMN risodb.S_SUPPLY_ABCD_KI.S_CODE_KOKYAKU IS 'Customer code';
COMMENT ON COLUMN risodb.S_SUPPLY_ABCD_KI.S_KANJ_KOKYAKUMEI IS 'Customer name (Kanji)';
COMMENT ON COLUMN risodb.S_SUPPLY_ABCD_KI.S_CODE_YUBIN IS 'Postal code';
COMMENT ON COLUMN risodb.S_SUPPLY_ABCD_KI.S_KANJ_JYUSHO IS 'Address (Kanji)';
COMMENT ON COLUMN risodb.S_SUPPLY_ABCD_KI.S_DATE_URIAGE_KI IS 'Sales period date';
COMMENT ON COLUMN risodb.S_SUPPLY_ABCD_KI.S_DATE_NEN IS 'Year date';
COMMENT ON COLUMN risodb.S_SUPPLY_ABCD_KI.S_KBN_NEWJG IS 'New judgment classification';
