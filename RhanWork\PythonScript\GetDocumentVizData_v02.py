"""
©2023 MicroStrategy. All rights reserved.
Application : 
Program     : 
Purpose     :ドキュメントの特定ビジュアリゼーションのcsvエクスポート
Modification History
When       : Who    : What
2024/02/29  : MSTR Ikeda : 新規作成
"""
# パッケージ読み込み
import os
import sys
sys.path.append( os.path.join( os.path.dirname(__file__), "." ) )
import logging
import datetime
import argparse
import pprint
import pandas as pd
import requests
import json
import pathlib
import traceback

# mstrio-api
from mstrio.connection import Connection

# 環境変数の読み込み(Constants.py)
from Constants import *

# common functions
from CommonFunctions import loadExcelData, exportToSharedFolder

# 変数設定
sTime = datetime.datetime.now().strftime( "%Y%m%d%H%M%S" )
sFileName = os.path.splitext( os.path.basename( os.path.basename(__file__) ) )[0]

# ログ設定
logger = logging.getLogger( __name__ )
logger.setLevel( logging.DEBUG )
handler = logging.StreamHandler()

logger.addHandler( handler )
#fmt = logging.Formatter( "%(asctime)s - %(name)s - %(levelname)s - %(message)s" )
fmt = logging.Formatter( "%(asctime)s - %(funcName)s - %(levelname)s - %(message)s" )
handler.setFormatter( fmt )
logger.propagate = False #これがないと2行ログがでてしまう。mstrioの作りのせいか。

if bUsingLogfile:
    filehandler = logging.FileHandler( f".\\log\\{sFileName}_{sTime}.log" )
    logger.addHandler( filehandler )
    filehandler.setFormatter( fmt )


# ログ出力サンプル
#logger.critical( "Log type is critical." )
#logger.error( "Log type is error." )
#logger.warning( "Log type is warning." )
#logger.info( "Log type is info." )
#logger.debug( "Log type is debug." )



def main():

    ####################################
    #　コマンドライン引数
    ####################################
    parser = argparse.ArgumentParser()
    parser.add_argument( "inputFile", type=str, help="File path for object list Excel" )
    parser.add_argument( "sharedFolder", type=str, help="Target folder path" )


    if bUsingCommandLine:
        
        ### args = parser.parse_args()
        args = parser.parse_args(args=["C:\\00work\\20MSTR\\00Case\\_PythonScript\\inputDocumentCSVExportList1.xlsx",
                                       "\\\\************\\share\\CSV_Data"
                                       ])
    else:
             
        args = parser.parse_args(args=["C:\\00work\\20MSTR\\00Case\\_PythonScript\\inputDocumentCSVExportList1.xlsx",
                                       "\\\\************\\share\\CSV_Data"
                                       ])
    
    ####################################
    # インプットファイル読み込み
    ####################################
    df_in, path = loadExcelData( args.inputFile )
    
    logger.info(f"Number of rows of input data is {len(df_in)}.")
        
    

    ####################################
    # ログイン
    ####################################
    mstr_conn = Connection( base_url = "http://************:8080/MicroStrategyLibrary/api", 
                            username = "Administrator",
                            password = "",
                            project_name = "MicroStrategy Tutorial",
                            )

    logger.info( f"Status is {mstr_conn.status()}." )

    # mstrio経由ではなく、Rest APIを直接利用ことも考慮し、tokenとsessionが取得できるか確認
    sToken = mstr_conn._session.headers["X-MSTR-AuthToken"]
    sSessionId = mstr_conn._session.cookies["JSESSIONID"]
    sProjId = mstr_conn.project_id
    #logger.debug( f"X-MSTR-AuthToken is {sToken}." )
    #logger.debug( f"mstr_conn.token is {mstr_conn.token}." )    
    #logger.debug( f"JSESSIONID is {sSessionId}." )

    i, isuc, ifai = 0, 0, 0 
    for index, row in df_in.iterrows():

        i = index

        try:
            sObjId = row["オブジェクトID"]
            sNodeKey = row["Node Key"]
            sFileName = row["ファイル名"]

            logger.info( f"Target object id = [{sObjId}], Node Key = [{sNodeKey}], output file = [{sFileName}]")

            ####################################
            # Documentの定義取得(Rest API) ※Update4.1ではエンドポイントなし
            ####################################

            bGetDefinition = False
            if bGetDefinition:

                logger.info( f"Get Document definition." )
                
                headers = { "Accept": "application/json", "X-MSTR-AuthToken": sToken, "X-MSTR-ProjectID": sProjId}
                cookies = { "JSESSIONID": sSessionId }

                response = requests.get( f"{mstr_base_url}/documents/{sObjId}/definition"
                                        ,headers = headers
                                        ,cookies = cookies
                                        ,verify = bVerify)
                
                instanceId = None

                if response.ok:

                    pprint.pprint( response.json() )

                else:
                    logger.debug( response.text )

                continue

            
            
            ####################################
            # Documentのインスタンス作成(Rest API)
            ####################################
                
            logger.info( f"Get Document Instance Id." )    
            
            headers = { "Accept": "application/json", "X-MSTR-AuthToken": sToken, "X-MSTR-ProjectID": sProjId}
            cookies = { "JSESSIONID": sSessionId }

            response = requests.post( f"{mstr_base_url}/documents/{sObjId}/instances"
                                    ,headers = headers
                                    ,cookies = cookies
                                    ,verify = bVerify)
            
            instanceId = None

            if response.ok:

                instanceId =  response.json()["mid"]
                logger.debug( f"Document Instance id is {instanceId}." )

            else:
                logger.debug( response.text )
            

            ####################################
            # Document内の特定visualizationのcsvデータ取得(Rest API)
            ####################################
                
            logger.info( f"Export visualization data to csv." )    
            
            #他とheader acceptの設定が異なる
            headers = { "Accept": "application/octet-stream", "X-MSTR-AuthToken": sToken, "X-MSTR-ProjectID": sProjId}
            cookies = { "JSESSIONID": sSessionId }
            
            response = requests.post( f"{mstr_base_url}/documents/{sObjId}/instances/{instanceId}/visualizations/{sNodeKey}/csv"
                                    ,headers = headers
                                    ,cookies = cookies
                                    ,verify = bVerify)
            
            logger.debug(response.status_code)

            if response.ok:

                # バイナリデータをCSVファイルに保存
                with open( f"{path.parent.parent}\\output\\{sFileName}", 'wb' ) as file:
                    for chunk in response.iter_content( chunk_size = 1024*2048 ):
                        file.write( chunk )

                logger.info( f"Save to {sFileName}.")


            else:
                logger.debug( response.text )


            ####################################
            # ファイルをネットワーク共有フォルダにコピー
            ####################################
            bCopyToTargetFolder = False

            logger.info( f"Copy a file to network shared folder." )   

            if bCopyToTargetFolder:
                exportToSharedFolder( args.sharedFolder, sOSUser , sOSPwd, f"{path.parent.parent}\\output\\{sFileName}" )

        except Exception as e:
            logger.error(f"Exception raised for row[{i}].", exc_info = True )
            ifai += 1
            pass

    
        else:
            logger.info(f"Successfully changed attribute for now[{i}].")
            isuc += 1

    logger.info(f"Number of successful transactions: {isuc}")
    logger.info(f"Number of failed transactions: {ifai}")

    ####################################
    # ログアウト
    ####################################
    try:
        mstr_conn.close()
        #a = 12 / 0
    except Exception as e:
        logger.error(f"Exception occurred in connection close", exc_info = True )
        t = traceback.format_exception(type(e), e, e.__traceback__)
        print(t)





if __name__=="__main__":
    main()