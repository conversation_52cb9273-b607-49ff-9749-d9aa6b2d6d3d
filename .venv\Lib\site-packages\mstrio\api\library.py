from mstrio.utils.error_handlers import <PERSON>rrorHandler


@ErrorHandler(err_msg="Error getting document with ID {id}")
def get_document(connection, id, error_msg=None):
    """Get information for a document with document Id.

    Args:
        connection: Strategy One REST API connection object
        id (string): Document ID
        error_msg (string, optional): Custom Error Message for Error Handling

    Returns:
        Complete HTTP response object.
    """
    endpoint = f'/api/library/{id}'
    return connection.get(endpoint=endpoint)


@ErrorHandler(err_msg="Error unpublishing document with ID {id}")
def unpublish_document(connection, id, error_msg=None):
    """Unpublish a previously published document. This makes the document no
    longer available in the library of each user it was originally published
    to.

    Args:
        connection: Strategy One REST API connection object
        id (string): Document ID
        error_msg (string, optional): Custom Error Message for Error Handling

    Returns:
        Complete HTTP response object.
    """
    endpoint = f'/api/library/{id}'
    return connection.delete(endpoint=endpoint)


@ErrorHandler(err_msg="Error unpublishing document with ID {document_id}")
def unpublish_document_for_user(connection, document_id, user_id, error_msg=None):
    """Unpublish a previously published document. This makes the document no
    longer available in the library of each user specified in `user_id`

    Args:
        connection: Strategy One REST API connection object
        document_id (string): Document ID
        user_id (string): user ID
        error_msg (string, optional): Custom Error Message for Error Handling

    Returns:
        Complete HTTP response object.
    """
    connection._validate_project_selected()
    endpoint = f'/api/library/{document_id}/recipients/{user_id}'
    return connection.delete(endpoint=endpoint)


@ErrorHandler(err_msg="Error getting library.")
def get_library(connection, error_msg=None):
    """Get the library for the authenticated user.

    Args:
        connection: Strategy One REST API connection object
        error_msg (string, optional): Custom Error Message for Error Handling

    Returns:
        Complete HTTP response object.
    """
    endpoint = '/api/library'
    return connection.get(endpoint=endpoint, headers={'X-MSTR-ProjectID': None})


@ErrorHandler(err_msg="Error publishing document.")
def publish_document(connection, body, error_msg=None):
    """Publish a document to users or user groups in a specific project.

    Args:
        connection: Strategy One REST API connection object
        body: JSON-formatted definition of the dataset. Generated by
            `utils.formjson()`.
        error_msg (string, optional): Custom Error Message for Error Handling

    Returns:
        Complete HTTP response object.
    """
    connection._validate_project_selected()
    endpoint = '/api/library'
    return connection.post(endpoint=endpoint, json=body)
