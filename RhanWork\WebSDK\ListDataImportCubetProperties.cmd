call common.cmd


@set MSTR_SERVER="jeol.cloud.microstrategy.com"
@rem MSTR_SERVER="jeolscript.cloud.microstrategy.com"
@rem MSTR_SERVER="***********"

@set MSTR_SERVER_PORT=0
@rem auth_mode 1=standard, 16=ldap
@set MSTR_AUTH_MODE=1
@set MSTR_PROJECT="Project_Prod"
@set MSTR_USR="c787_prd_administrator"
@set MSTR_PWD="Mstr8558"


@rem "-Djava.locale.providers option" for MSTR2021
@rem フォルダ指定
@rem java -Djava.locale.providers=COMPAT,CLDR,SPI -Xmx1g com.microstrategy.sdk.tools.ListDataImportCubeProperties %MSTR_SERVER% %MSTR_SERVER_PORT% %MSTR_AUTH_MODE% %MSTR_PROJECT% %MSTR_USR% %MSTR_PWD% 737CB3B64B25C6DC129BE5AE11AB8147 folder >> log_ListDataImportCubeProperties.txt

@rem キューブ指定
java -Djava.locale.providers=COMPAT,CLDR,SPI -Xmx1g com.microstrategy.sdk.tools.ListDataImportCubeProperties %MSTR_SERVER% %MSTR_SERVER_PORT% %MSTR_AUTH_MODE% %MSTR_PROJECT% %MSTR_USR% %MSTR_PWD% 08CD88FF495D46F4941D72B330725C3F >> log_ListDataImportCubeProperties.txt
