-- Deep Analysis: Find Column Objects Used by Metrics in Report CS0938327FFReport2
-- Database: poc_metadata (PostgreSQL) - Server: 10.27.72.202:5432
-- Purpose: Find actual "column" managed objects that metrics depend on

-- =====================================================================================
-- STEP 1: DISCOVER ALL MICROSTRATEGY TABLES
-- =====================================================================================

-- Find all MicroStrategy metadata tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_name LIKE '%dss%' 
ORDER BY table_name;

-- =====================================================================================
-- STEP 2: ANALYZE OBJECT TYPES TO FIND COLUMN OBJECTS
-- =====================================================================================

-- Get comprehensive object type analysis
SELECT 
    object_type,
    subtype,
    COUNT(*) as count,
    MIN(object_name) as sample_name_1,
    MAX(object_name) as sample_name_2
FROM dssmdobjinfo
GROUP BY object_type, subtype
ORDER BY object_type, subtype;

-- =====================================================================================
-- STEP 3: FIND METRICS AND THEIR DIRECT DEPENDENCIES
-- =====================================================================================

-- Get the two metrics from the report and what they depend on
WITH report_metrics AS (
    SELECT DISTINCT
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3
        AND r.subtype = 768
        AND m.object_type = 4
        AND m.subtype = 1024
)
SELECT 
    rm.metric_name,
    rm.metric_id,
    c.object_id as dependent_object_id,
    c.object_name as dependent_object_name,
    c.object_type,
    c.subtype,
    CASE 
        WHEN c.object_type = 1 THEN 'Attribute Form'
        WHEN c.object_type = 2 THEN 'Fact'
        WHEN c.object_type = 3 THEN 'Report'
        WHEN c.object_type = 4 THEN 'Metric'
        WHEN c.object_type = 5 THEN 'Filter'
        WHEN c.object_type = 6 THEN 'Hierarchy'
        WHEN c.object_type = 7 THEN 'Transformation'
        WHEN c.object_type = 8 THEN 'Function'
        WHEN c.object_type = 9 THEN 'Operator'
        WHEN c.object_type = 10 THEN 'Consolidation'
        WHEN c.object_type = 11 THEN 'Custom Group'
        WHEN c.object_type = 12 THEN 'Attribute'
        WHEN c.object_type = 13 THEN 'Dimension'
        WHEN c.object_type = 14 THEN 'Column'
        WHEN c.object_type = 15 THEN 'Table'
        WHEN c.object_type = 16 THEN 'Database Instance'
        WHEN c.object_type = 17 THEN 'Database Connection'
        WHEN c.object_type = 18 THEN 'Database Login'
        WHEN c.object_type = 19 THEN 'Project'
        WHEN c.object_type = 20 THEN 'User'
        WHEN c.object_type = 21 THEN 'Form'
        WHEN c.object_type = 22 THEN 'Folder'
        ELSE 'Other (' || c.object_type || ')'
    END as object_type_description
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
ORDER BY rm.metric_name, c.object_type, c.object_name;

-- =====================================================================================
-- STEP 4: RECURSIVE SEARCH FOR COLUMN OBJECTS
-- =====================================================================================

-- Recursively find all dependencies to locate actual column objects
WITH RECURSIVE metric_dependencies AS (
    -- Base case: Get metrics from the report
    SELECT 
        m.object_id as source_metric_id,
        m.object_name as source_metric_name,
        m.object_id as current_object_id,
        m.object_name as current_object_name,
        m.object_type as current_object_type,
        m.subtype as current_subtype,
        0 as depth_level
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3
        AND r.subtype = 768
        AND m.object_type = 4
        AND m.subtype = 1024
    
    UNION ALL
    
    -- Recursive case: Follow dependencies
    SELECT 
        md.source_metric_id,
        md.source_metric_name,
        c.object_id,
        c.object_name,
        c.object_type,
        c.subtype,
        md.depth_level + 1
    FROM metric_dependencies md
    INNER JOIN dssmdobjdepn d ON md.current_object_id = d.object_id
    INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
    WHERE md.depth_level < 5  -- Limit recursion depth
)
SELECT DISTINCT
    md.source_metric_name,
    md.current_object_id as object_id,
    md.current_object_name as object_name,
    md.current_object_type as object_type,
    md.current_subtype as subtype,
    md.depth_level,
    CASE 
        WHEN md.current_object_type = 1 THEN 'Attribute Form'
        WHEN md.current_object_type = 2 THEN 'Fact'
        WHEN md.current_object_type = 12 THEN 'Attribute'
        WHEN md.current_object_type = 14 THEN 'Column'
        WHEN md.current_object_type = 15 THEN 'Table'
        WHEN md.current_object_type = 21 THEN 'Form'
        ELSE 'Other (' || md.current_object_type || ')'
    END as object_type_description
FROM metric_dependencies md
WHERE md.current_object_type IN (1, 2, 12, 14, 15, 21)  -- Focus on data-related objects
ORDER BY md.source_metric_name, md.depth_level, md.current_object_type, md.current_object_name;

-- =====================================================================================
-- STEP 5: SEARCH FOR COLUMN OBJECTS (TYPE 14) SPECIFICALLY
-- =====================================================================================

-- Look for objects with type 14 (Column) in the entire database
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    create_time,
    mod_time,
    description
FROM dssmdobjinfo
WHERE object_type = 14  -- Column objects
ORDER BY object_name
LIMIT 50;

-- =====================================================================================
-- STEP 6: FIND FACT OBJECTS (TYPE 2) THAT MIGHT BE RELATED
-- =====================================================================================

-- Look for fact objects that might be related to our metrics
WITH report_metrics AS (
    SELECT DISTINCT m.object_id as metric_id, m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3 AND r.subtype = 768
        AND m.object_type = 4 AND m.subtype = 1024
)
SELECT DISTINCT
    rm.metric_name,
    f.object_id as fact_id,
    f.object_name as fact_name,
    f.object_type,
    f.subtype,
    f.description
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo f ON d.depn_objid = f.object_id
WHERE f.object_type = 2  -- Fact objects
ORDER BY rm.metric_name, f.object_name;

-- =====================================================================================
-- STEP 7: ANALYZE TABLE OBJECTS (TYPE 15) FOR COLUMN INFORMATION
-- =====================================================================================

-- Check if there are additional tables that might contain column definitions
SELECT table_name, column_name, data_type
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name LIKE '%dss%col%'
    OR table_name LIKE '%dss%attr%'
    OR table_name LIKE '%dss%fact%'
ORDER BY table_name, ordinal_position;

-- =====================================================================================
-- STEP 8: DETAILED COLUMN OBJECT INFORMATION
-- =====================================================================================

-- If we find column objects, get their detailed information
-- This query will show detailed info for any column objects found
SELECT 
    c.object_id,
    c.object_name,
    c.object_type,
    c.subtype,
    c.description,
    c.create_time,
    c.mod_time,
    c.owner_id,
    c.parent_id,
    c.hidden,
    c.object_state,
    -- Try to find what table this column belongs to
    t.object_name as table_name,
    t.object_type as table_type
FROM dssmdobjinfo c
LEFT JOIN dssmdobjdepn d ON c.object_id = d.depn_objid
LEFT JOIN dssmdobjinfo t ON d.object_id = t.object_id AND t.object_type = 15
WHERE c.object_type = 14  -- Column objects
ORDER BY c.object_name;

-- =====================================================================================
-- STEP 9: ALTERNATIVE SEARCH - LOOK FOR COLUMN-LIKE OBJECTS
-- =====================================================================================

-- Search for objects that might be columns based on naming patterns
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    description
FROM dssmdobjinfo
WHERE (object_name ILIKE '%column%' 
    OR object_name ILIKE '%col%'
    OR object_name ILIKE '%field%'
    OR object_type IN (1, 2, 12, 14, 21))  -- Potential column-related types
    AND object_name NOT ILIKE '%table%'
    AND object_name NOT ILIKE '%report%'
ORDER BY object_type, object_name
LIMIT 100;

-- =====================================================================================
-- EXECUTION INSTRUCTIONS:
-- =====================================================================================
-- 1. Run Step 1 to see all available MicroStrategy tables
-- 2. Run Step 2 to understand all object types in the database
-- 3. Run Step 3 to see what metrics directly depend on
-- 4. Run Step 4 for recursive dependency analysis
-- 5. Run Step 5 to find Column objects (type 14)
-- 6. Run Step 6 to find Fact objects (type 2)
-- 7. Run Step 7 to check for additional column-related tables
-- 8. Run Step 8 for detailed column object information
-- 9. Run Step 9 for alternative column object search
-- =====================================================================================
