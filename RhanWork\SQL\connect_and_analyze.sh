#!/bin/bash

# Connection script for MicroStrategy metadata database analysis
# Usage: ./connect_and_analyze.sh [password]

HOST="************"
PORT="5432"
USER="mstr"
DATABASE="poc_metadata"

# Set password if provided as argument, otherwise prompt
if [ "$1" != "" ]; then
    export PGPASSWORD="$1"
else
    echo "Please enter the password for user 'mstr':"
    read -s PGPASSWORD
    export PGPASSWORD
fi

echo "Connecting to PostgreSQL database..."
echo "Host: $HOST:$PORT"
echo "Database: $DATABASE"
echo "User: $USER"
echo ""

# Test connection
echo "Testing connection..."
psql -h $HOST -p $PORT -U $USER -d $DATABASE -c "SELECT version();" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ Connection successful!"
    echo ""
else
    echo "✗ Connection failed. Please check credentials."
    exit 1
fi

# Run analysis queries
echo "Running deep column analysis..."
echo "================================"

echo "1. Finding all MicroStrategy tables..."
psql -h $HOST -p $PORT -U $USER -d $DATABASE -c "
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_name LIKE '%dss%' 
ORDER BY table_name;
"

echo ""
echo "2. Analyzing object types..."
psql -h $HOST -p $PORT -U $USER -d $DATABASE -c "
SELECT 
    object_type,
    subtype,
    COUNT(*) as count,
    MIN(object_name) as sample_name
FROM dssmdobjinfo
GROUP BY object_type, subtype
ORDER BY object_type, subtype
LIMIT 30;
"

echo ""
echo "3. Finding metrics in the report..."
psql -h $HOST -p $PORT -U $USER -d $DATABASE -c "
SELECT DISTINCT
    m.object_id as metric_id,
    m.object_name as metric_name,
    m.object_type,
    m.subtype
FROM dssmdobjinfo r
INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
WHERE r.object_name = 'CS0938327FFReport2'
    AND r.object_type = 3
    AND r.subtype = 768
    AND m.object_type = 4
    AND m.subtype = 1024;
"

echo ""
echo "4. Finding what metrics depend on..."
psql -h $HOST -p $PORT -U $USER -d $DATABASE -c "
WITH report_metrics AS (
    SELECT DISTINCT
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3 AND r.subtype = 768
        AND m.object_type = 4 AND m.subtype = 1024
)
SELECT 
    rm.metric_name,
    c.object_id as dependent_object_id,
    c.object_name as dependent_object_name,
    c.object_type,
    c.subtype,
    CASE 
        WHEN c.object_type = 1 THEN 'Attribute Form'
        WHEN c.object_type = 2 THEN 'Fact'
        WHEN c.object_type = 12 THEN 'Attribute'
        WHEN c.object_type = 14 THEN 'Column'
        WHEN c.object_type = 15 THEN 'Table'
        WHEN c.object_type = 21 THEN 'Form'
        ELSE 'Other (' || c.object_type || ')'
    END as object_type_description
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
ORDER BY rm.metric_name, c.object_type;
"

echo ""
echo "5. Looking for Column objects (type 14)..."
psql -h $HOST -p $PORT -U $USER -d $DATABASE -c "
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    description
FROM dssmdobjinfo
WHERE object_type = 14
ORDER BY object_name
LIMIT 20;
"

echo ""
echo "6. Looking for Fact objects (type 2)..."
psql -h $HOST -p $PORT -U $USER -d $DATABASE -c "
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    description
FROM dssmdobjinfo
WHERE object_type = 2
ORDER BY object_name
LIMIT 20;
"

echo ""
echo "Analysis complete!"
echo "Check the results above to identify column objects and their relationships."
