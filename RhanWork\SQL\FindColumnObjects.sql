-- Find Column Objects Used by Metrics in Report CS0938327FFReport2
-- Focus on finding actual "column" managed objects in MicroStrategy
-- Database: poc_metadata (PostgreSQL) - Server: 10.27.72.202:5432

-- =====================================================================================
-- STEP 1: GET METRICS FROM THE REPORT
-- =====================================================================================

-- First, let's get the metrics we found earlier
WITH report_metrics AS (
    SELECT DISTINCT
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3
        AND r.subtype = 768
        AND m.object_type = 4
        AND m.subtype = 1024
)
SELECT 
    metric_id,
    metric_name,
    'Metric IDs for further analysis' as note
FROM report_metrics;

-- =====================================================================================
-- STEP 2: FIND ALL DEPENDENCIES OF THESE METRICS
-- =====================================================================================

-- Get everything that these metrics depend on
WITH report_metrics AS (
    SELECT DISTINCT
        m.object_id as metric_id,
        m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3 AND r.subtype = 768
        AND m.object_type = 4 AND m.subtype = 1024
)
SELECT 
    rm.metric_name,
    c.object_id,
    c.object_name,
    c.object_type,
    c.subtype,
    c.description,
    c.create_time,
    c.mod_time,
    CASE 
        WHEN c.object_type = 1 THEN 'Attribute Form'
        WHEN c.object_type = 2 THEN 'Fact'
        WHEN c.object_type = 3 THEN 'Report'
        WHEN c.object_type = 4 THEN 'Metric'
        WHEN c.object_type = 5 THEN 'Filter'
        WHEN c.object_type = 6 THEN 'Hierarchy'
        WHEN c.object_type = 7 THEN 'Transformation'
        WHEN c.object_type = 8 THEN 'Function'
        WHEN c.object_type = 12 THEN 'Attribute'
        WHEN c.object_type = 14 THEN 'Column'
        WHEN c.object_type = 15 THEN 'Table'
        WHEN c.object_type = 21 THEN 'Form'
        ELSE 'Other (' || c.object_type || ')'
    END as object_type_description
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
ORDER BY rm.metric_name, c.object_type, c.object_name;

-- =====================================================================================
-- STEP 3: RECURSIVE SEARCH FOR COLUMN OBJECTS
-- =====================================================================================

-- Recursively search through dependencies to find column objects
WITH RECURSIVE metric_dependencies AS (
    -- Base: Start with metrics from the report
    SELECT 
        m.object_id as source_metric_id,
        m.object_name as source_metric_name,
        m.object_id as current_object_id,
        m.object_name as current_object_name,
        m.object_type as current_object_type,
        m.subtype as current_subtype,
        0 as depth_level,
        ARRAY[m.object_id] as path
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3 AND r.subtype = 768
        AND m.object_type = 4 AND m.subtype = 1024
    
    UNION ALL
    
    -- Recursive: Follow dependencies
    SELECT 
        md.source_metric_id,
        md.source_metric_name,
        c.object_id,
        c.object_name,
        c.object_type,
        c.subtype,
        md.depth_level + 1,
        md.path || c.object_id
    FROM metric_dependencies md
    INNER JOIN dssmdobjdepn d ON md.current_object_id = d.object_id
    INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
    WHERE md.depth_level < 4  -- Limit recursion depth
        AND NOT (c.object_id = ANY(md.path))  -- Prevent cycles
)
SELECT DISTINCT
    md.source_metric_name,
    md.current_object_id as object_id,
    md.current_object_name as object_name,
    md.current_object_type as object_type,
    md.current_subtype as subtype,
    md.depth_level,
    CASE 
        WHEN md.current_object_type = 1 THEN 'Attribute Form'
        WHEN md.current_object_type = 2 THEN 'Fact'
        WHEN md.current_object_type = 12 THEN 'Attribute'
        WHEN md.current_object_type = 14 THEN 'Column'
        WHEN md.current_object_type = 15 THEN 'Table'
        WHEN md.current_object_type = 21 THEN 'Form'
        ELSE 'Other (' || md.current_object_type || ')'
    END as object_type_description
FROM metric_dependencies md
WHERE md.current_object_type IN (1, 2, 12, 14, 15, 21)  -- Focus on data objects
ORDER BY md.source_metric_name, md.depth_level, md.current_object_type, md.current_object_name;

-- =====================================================================================
-- STEP 4: SEARCH FOR COLUMN OBJECTS (TYPE 14) IN ENTIRE DATABASE
-- =====================================================================================

-- Look for all column objects in the database
SELECT 
    object_id,
    object_name,
    object_type,
    subtype,
    description,
    create_time,
    mod_time,
    parent_id,
    owner_id
FROM dssmdobjinfo
WHERE object_type = 14  -- Column objects
ORDER BY object_name;

-- =====================================================================================
-- STEP 5: FIND FACT OBJECTS AND THEIR RELATIONSHIPS
-- =====================================================================================

-- Look for fact objects that might contain column information
WITH report_metrics AS (
    SELECT DISTINCT m.object_id as metric_id, m.object_name as metric_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d ON r.object_id = d.object_id
    INNER JOIN dssmdobjinfo m ON d.depn_objid = m.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3 AND r.subtype = 768
        AND m.object_type = 4 AND m.subtype = 1024
)
SELECT DISTINCT
    rm.metric_name,
    f.object_id as fact_id,
    f.object_name as fact_name,
    f.object_type,
    f.subtype,
    f.description,
    f.create_time,
    f.mod_time
FROM report_metrics rm
INNER JOIN dssmdobjdepn d ON rm.metric_id = d.object_id
INNER JOIN dssmdobjinfo f ON d.depn_objid = f.object_id
WHERE f.object_type = 2  -- Fact objects
ORDER BY rm.metric_name, f.object_name;

-- =====================================================================================
-- STEP 6: DETAILED ANALYSIS OF ATTRIBUTE OBJECTS
-- =====================================================================================

-- Since we found attributes (type 12), let's see what they depend on
WITH report_attributes AS (
    SELECT DISTINCT
        a.object_id as attribute_id,
        a.object_name as attribute_name
    FROM dssmdobjinfo r
    INNER JOIN dssmdobjdepn d1 ON r.object_id = d1.object_id
    INNER JOIN dssmdobjinfo m ON d1.depn_objid = m.object_id
    INNER JOIN dssmdobjdepn d2 ON m.object_id = d2.object_id
    INNER JOIN dssmdobjinfo a ON d2.depn_objid = a.object_id
    WHERE r.object_name = 'CS0938327FFReport2'
        AND r.object_type = 3 AND r.subtype = 768
        AND m.object_type = 4 AND m.subtype = 1024
        AND a.object_type = 12  -- Attributes
)
SELECT 
    ra.attribute_name,
    c.object_id,
    c.object_name,
    c.object_type,
    c.subtype,
    c.description,
    CASE 
        WHEN c.object_type = 1 THEN 'Attribute Form'
        WHEN c.object_type = 2 THEN 'Fact'
        WHEN c.object_type = 14 THEN 'Column'
        WHEN c.object_type = 15 THEN 'Table'
        WHEN c.object_type = 21 THEN 'Form'
        ELSE 'Other (' || c.object_type || ')'
    END as object_type_description
FROM report_attributes ra
INNER JOIN dssmdobjdepn d ON ra.attribute_id = d.object_id
INNER JOIN dssmdobjinfo c ON d.depn_objid = c.object_id
ORDER BY ra.attribute_name, c.object_type, c.object_name;

-- =====================================================================================
-- STEP 7: CHECK FOR ADDITIONAL METADATA TABLES
-- =====================================================================================

-- Look for other tables that might contain column definitions
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND (table_name LIKE '%dss%col%'
         OR table_name LIKE '%dss%attr%'
         OR table_name LIKE '%dss%fact%'
         OR table_name LIKE '%dss%form%')
ORDER BY table_name;

-- =====================================================================================
-- EXECUTION NOTES:
-- =====================================================================================
-- This query set will help identify:
-- 1. The metrics in the report (Step 1)
-- 2. What these metrics directly depend on (Step 2)
-- 3. Recursive dependencies to find column objects (Step 3)
-- 4. All column objects in the database (Step 4)
-- 5. Fact objects related to the metrics (Step 5)
-- 6. What attributes depend on (Step 6)
-- 7. Additional metadata tables (Step 7)
-- =====================================================================================
