# mstrio-api		
from mstrio.connection import Connection		
import traceback
import requests

from mstrio.object_management import (		
    Translation,		
    list_translations	
)
		
mstr_base_url = "http://rhanvm2019:8080/MicroStrategyLibrary/api"		
mstr_username = "administrator"		
mstr_password = ""		
mstr_project_name = "MicroStrategy Tutorial"		
bVerify = False

def setHeaders(authToken, project_id):
    ### print("----->Start setHeaders")
    headers = {'X-MSTR-AuthToken': authToken,
               'Content-Type': 'application/json',#IMPORTANT!
               'Accept': 'application/json',
               'X-MSTR-ProjectID': project_id}
    ### print("----->End setHeaders")
    return headers

def doGet(base_url, auth_token, cookies, project_id, sObjId):
    print("----->Start doGet")	
    headers = setHeaders(auth_token, project_id)

    # Get object info
    response = requests.get( f"{base_url}/model/metrics/{sObjId}"
                                        , headers = headers
                                        , cookies = cookies
                                        , verify = bVerify)

    if response.ok:
        print("Error: " + str(response.raise_for_status()) + "   ||   HTTP Status Code: " + str(response.status_code))
        return response.json()
    else:
        print("HTTP %i - %s, Message %s" % (response.status_code, response.reason, response.text))
    	
    print("----->End doGet")

def doPost(base_url, auth_token, cookies, project_id, sObjId):

    return

def createChangeSet():
    return

def main():	
    print("--->Start main")	
    try:
        # Connect to library
        CONN = Connection(base_url = mstr_base_url, 
                                username = mstr_username, 
                                password = mstr_password, 
                                project_name = mstr_project_name, 	
                                ssl_verify = bVerify 
                                )
        
        # Metric ID
        sObjectId = "B8EEE04247E9BD273297DD9CCCC43566"
            
        # Get token, project_id
        sToken = CONN._session.headers["X-MSTR-AuthToken"]
        sSessionId = CONN._session.cookies["JSESSIONID"]
        sProjId = CONN.project_id
        sCookies = CONN._session.cookies

        # Execute Rest API, get the metric info
        sResult = doGet(mstr_base_url, sToken, sCookies, sProjId, sObjectId)

        # Print out the result
        ###print("--->sResult:" + str(sResult))
        print("--->Metric format:" + str(sResult["format"]))
        sFormat = sResult["format"]



        CONN.close()

    except Exception as e:
        t = traceback.format_exception(type(e), e, e.__traceback__)
        print(t)

    print("--->End main")

# Call the main function
if __name__ == "__main__":
    main()