#MSTR Environment Connection

mstr_base_url = "http://10.27.72.221:8080/MicroStrategyLibrary/api"
#mstr_base_url = "https://172.31.2.22/MicroStrategyLibrary/api"
mstr_username = "administrator"
mstr_password = ""
mstr_project_name = "MicroStrategy Tutorial"
#mstr_project_name = "Platform Analytics"
mstr_auth_mode = "1"


#Python Environment Parameter
bUsingLogfile = True
bUsingCommandLine = False


#Proxy
bUsingProxy = False
http_proxy = "http://192.168.66.20/"
https_proxy = "http://192.168.66.20/"

#SSL
bVerify = False

#OS User
sOSUser = "10.27.72.221\administrator"
sOSPwd = "m$tr!23"