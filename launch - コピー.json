{"workbench.colorTheme": "Default Light+", "editor.minimap.enabled": false, "terminal.integrated.shell.windows": "C:\\WINDOWS\\System32\\cmd.exe", "security.workspace.trust.untrustedFiles": "open", "editor.fontFamily": "Consolas, 'Courier New', monospace, 'ＭＳ 明朝', 'MS UI Gothic'", "files.autoGuessEncoding": true, "files.associations": {"*.xml": "xml", "*.jsp": "jsp"}, "[python]": {"editor.formatOnType": true}, "[ashx]": {}, "editor.largeFileOptimizations": false, "redhat.telemetry.enabled": true, "launch": {"configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": false}]}, "workbench.colorCustomizations": {}}