from mstrio.connection import Connection
from mstrio.project_objects import Report
from mstrio.object_management import Folder
from mstrio.types import ObjectTypes
import os
from dotenv import load_dotenv
import pandas as pd
from datetime import datetime

# Load environment variables from .env file
load_dotenv()

def connect_to_mstr():
    """Connect to MicroStrategy server using environment variables"""
    try:
        conn = Connection(
            base_url='http://rhanvm2019:8080/MicroStrategyLibrary/api',
            username='administrator',
            password='',
            project_id='B7CA92F04B9FAE8D941C3E9B7E0CD754'
        )
        return conn
    except Exception as e:
        print(f"Error connecting to MicroStrategy: {str(e)}")
        return None

def get_reports_in_folder(conn, folder_id, indent=0, parent_folder="", report_list=None):
    """Get list of reports in a specific folder and its subfolders"""
    if report_list is None:
        report_list = []
        
    try:
        # Get the folder object
        folder = Folder(connection=conn, id=folder_id)
        current_folder = folder.name
        print(f"{'  ' * indent}Folder: {current_folder}")
        
        # Get all reports in the current folder
        reports = folder.get_contents(type=ObjectTypes.REPORT_DEFINITION)
        
        # Add reports to the list
        for report in reports:
            report_list.append({
                'Parent Folder': parent_folder,
                'Folder Name': current_folder,
                'Report Name': report.name
            })
            print(f"{'  ' * indent}- {report.name}")
        
        # Get all subfolders
        subfolders = folder.get_contents(type=ObjectTypes.FOLDER)
        
        # Recursively process each subfolder
        for subfolder in subfolders:
            get_reports_in_folder(conn, subfolder.id, indent + 1, current_folder, report_list)
            
        return report_list
    except Exception as e:
        print(f"Error getting reports: {str(e)}")
        return report_list

def main():
    # Connect to MicroStrategy
    conn = connect_to_mstr()
    if not conn:
        return

    # Use the folder ID directly
    folder_id = '5C55199448CEB276F93ABB8A315EF7F7'
    
    print("\nSearching for reports in all folders...")
    # Get reports in the folder and all subfolders
    report_list = get_reports_in_folder(conn, folder_id)
    
    # Create DataFrame and export to Excel
    if report_list:
        df = pd.DataFrame(report_list)
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_filename = f"report_list_{timestamp}.xlsx"
        
        # Export to Excel
        df.to_excel(excel_filename, index=False)
        print(f"\nReport list has been exported to: {excel_filename}")
    else:
        print("\nNo reports found to export.")
    
    # Print total count
    print(f"\nTotal number of reports found: {len(report_list)}")

if __name__ == "__main__":
    main() 