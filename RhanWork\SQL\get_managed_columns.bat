@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM Database connection parameters
set HOST=************
set PORT=5432
set USER=mstr
set DATABASE=poc_metadata
set PGPASSWORD=zJ7MhP8mdRCI
set PGCLIENTENCODING=UTF8

REM Check if report name parameter is provided
if "%~1"=="" (
    echo.
    echo ERROR: Report name parameter is required!
    echo.
    echo Usage: %~nx0 "ReportName"
    echo Example: %~nx0 "CS0938327FFReport2"
    echo.
    pause
    exit /b 1
)

set "REPORT_NAME=%~1"

echo =====================================================================================
echo MicroStrategy Managed Column Analysis
echo =====================================================================================
echo Report Name: %REPORT_NAME%
echo Database: %DATABASE% on %HOST%:%PORT%
echo User: %USER%
echo.

REM Test database connection
echo Testing database connection...
psql -h %HOST% -p %PORT% -U %USER% -d %DATABASE% -c "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Cannot connect to database!
    echo Please check:
    echo - Database server is running
    echo - Network connectivity
    echo - Credentials are correct
    pause
    exit /b 1
)
echo ✓ Database connection successful!
echo.

REM Create the SQL query with the report name parameter
set "SQL_QUERY=SELECT DISTINCT r.object_name as report_name, r.object_id as report_id, m.object_name as metric_name, m.object_id as metric_id, mc.object_name as managed_column_name, mc.object_id as managed_column_id FROM dssmdobjinfo r INNER JOIN dssmdobjdepn d1 ON r.object_id = d1.object_id INNER JOIN dssmdobjinfo m ON d1.depn_objid = m.object_id INNER JOIN dssmdobjdepn d2 ON m.object_id = d2.object_id INNER JOIN dssmdobjinfo mc ON d2.depn_objid = mc.object_id WHERE r.object_name = '%REPORT_NAME%' AND r.object_type = 3 AND r.subtype = 768 AND m.object_type = 4 AND m.subtype = 1024 AND mc.object_type = 26 AND mc.subtype = 6656 AND mc.extended_type = 3 ORDER BY m.object_name, mc.object_name;"

echo Executing query for report: %REPORT_NAME%
echo =====================================================================================

REM Execute the query and capture results
psql -h %HOST% -p %PORT% -U %USER% -d %DATABASE% -c "%SQL_QUERY%" 2>error.tmp

REM Check if there were any errors
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Query execution failed!
    echo Error details:
    type error.tmp
    del error.tmp >nul 2>&1
    pause
    exit /b 1
)

REM Clean up temporary files
del error.tmp >nul 2>&1

echo.
echo =====================================================================================
echo Analysis completed successfully!
echo.
echo Column descriptions:
echo - report_name: Name of the MicroStrategy report
echo - report_id: Unique identifier of the report
echo - metric_name: Name of the metric within the report
echo - metric_id: Unique identifier of the metric
echo - managed_column_name: Name of the managed column used by the metric
echo - managed_column_id: Unique identifier of the managed column
echo =====================================================================================

REM Check if we want to save results to file
echo.
set /p SAVE_TO_FILE="Save results to file? (y/n): "
if /i "%SAVE_TO_FILE%"=="y" (
    set OUTPUT_FILE=managed_columns_%REPORT_NAME%_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.csv
    set OUTPUT_FILE=!OUTPUT_FILE: =0!
    echo.
    echo Saving results to: !OUTPUT_FILE!
    
    REM Create CSV header
    echo report_name,report_id,metric_name,metric_id,managed_column_name,managed_column_id > "!OUTPUT_FILE!"
    
    REM Execute query with CSV format - create a separate CSV query
    set "CSV_QUERY=COPY (SELECT DISTINCT r.object_name as report_name, r.object_id as report_id, m.object_name as metric_name, m.object_id as metric_id, mc.object_name as managed_column_name, mc.object_id as managed_column_id FROM dssmdobjinfo r INNER JOIN dssmdobjdepn d1 ON r.object_id = d1.object_id INNER JOIN dssmdobjinfo m ON d1.depn_objid = m.object_id INNER JOIN dssmdobjdepn d2 ON m.object_id = d2.object_id INNER JOIN dssmdobjinfo mc ON d2.depn_objid = mc.object_id WHERE r.object_name = '%REPORT_NAME%' AND r.object_type = 3 AND r.subtype = 768 AND m.object_type = 4 AND m.subtype = 1024 AND mc.object_type = 26 AND mc.subtype = 6656 AND mc.extended_type = 3 ORDER BY m.object_name, mc.object_name) TO STDOUT WITH CSV;"

    psql -h %HOST% -p %PORT% -U %USER% -d %DATABASE% -c "!CSV_QUERY!" >> "!OUTPUT_FILE!"
    
    echo ✓ Results saved to: !OUTPUT_FILE!
)

echo.
pause
